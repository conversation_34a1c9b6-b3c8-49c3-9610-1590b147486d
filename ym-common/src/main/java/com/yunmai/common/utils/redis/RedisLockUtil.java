package com.yunmai.common.utils.redis;

import com.yunmai.common.core.redis.RedisCache;
import com.yunmai.common.utils.spring.SpringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁工具类
 * 
 * <AUTHOR>
 */
public class RedisLockUtil {
    
    private static final String LOCK_PREFIX = "distributed_lock:";
    private static final String UNLOCK_LUA_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else return 0 end";
    
    /**
     * 尝试获取分布式锁
     * 
     * @param lockKey 锁的key
     * @param expireTime 锁的过期时间
     * @param timeUnit 时间单位
     * @return 锁的值，获取失败返回null
     */
    public static String tryLock(String lockKey, long expireTime, TimeUnit timeUnit) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String fullLockKey = LOCK_PREFIX + lockKey;
        
        // 检查锁是否已存在
        String existingValue = redisCache.getCacheObject(fullLockKey);
        if (existingValue != null) {
            return null; // 锁已被占用
        }
        
        // 生成唯一的锁值
        String lockValue = UUID.randomUUID().toString();
        
        // 设置锁
        redisCache.setCacheObject(fullLockKey, lockValue, (int)expireTime, timeUnit);
        
        // 再次检查确保设置成功（防止并发情况下的竞态条件）
        String setValue = redisCache.getCacheObject(fullLockKey);
        if (lockValue.equals(setValue)) {
            return lockValue;
        }
        
        return null;
    }
    
    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁的key
     * @param lockValue 锁的值
     * @return 是否释放成功
     */
    public static boolean releaseLock(String lockKey, String lockValue) {
        if (lockValue == null) {
            return false;
        }
        
        try {
            RedisTemplate<Object, Object> redisTemplate = SpringUtils.getBean("redisTemplate", RedisTemplate.class);
            String fullLockKey = LOCK_PREFIX + lockKey;
            
            DefaultRedisScript<Long> script = new DefaultRedisScript<>();
            script.setScriptText(UNLOCK_LUA_SCRIPT);
            script.setResultType(Long.class);
            
            Long result = redisTemplate.execute(script, Collections.singletonList(fullLockKey), lockValue);
            return result != null && result > 0;
        } catch (Exception e) {
            // 释放锁失败，记录日志但不影响业务
            return false;
        }
    }
    
    /**
     * 检查锁是否存在
     * 
     * @param lockKey 锁的key
     * @return 是否存在
     */
    public static boolean isLocked(String lockKey) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String fullLockKey = LOCK_PREFIX + lockKey;
        return redisCache.getCacheObject(fullLockKey) != null;
    }
}
