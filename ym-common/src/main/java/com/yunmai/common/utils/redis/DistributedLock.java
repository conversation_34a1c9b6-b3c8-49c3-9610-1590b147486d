package com.yunmai.common.utils.redis;

import com.yunmai.common.core.redis.RedisCache;
import com.yunmai.common.utils.spring.SpringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的分布式锁实现
 * 支持自动过期和安全释放
 * 
 * <AUTHOR>
 */
public class DistributedLock {
    
    private static final String LOCK_PREFIX = "distributed_lock:";
    
    // Lua脚本：原子性地设置锁（SET NX EX）
    private static final String LOCK_LUA_SCRIPT = 
        "if redis.call('exists', KEYS[1]) == 0 then " +
        "return redis.call('setex', KEYS[1], ARGV[2], ARGV[1]) " +
        "else return 0 end";
    
    // Lua脚本：原子性地释放锁
    private static final String UNLOCK_LUA_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else return 0 end";
    
    private final String lockKey;
    private final String lockValue;
    private final RedisCache redisCache;
    
    /**
     * 构造函数
     * @param lockKey 锁的键
     */
    public DistributedLock(String lockKey) {
        this.lockKey = LOCK_PREFIX + lockKey;
        this.lockValue = UUID.randomUUID().toString();
        this.redisCache = SpringUtils.getBean(RedisCache.class);
    }
    
    /**
     * 尝试获取锁
     * @param expireTime 锁的过期时间
     * @param timeUnit 时间单位
     * @return 是否获取成功
     */
    public boolean tryLock(long expireTime, TimeUnit timeUnit) {
        try {
            // 简化实现：先检查是否存在，不存在则设置
            String existingValue = redisCache.getCacheObject(lockKey);
            if (existingValue != null) {
                return false; // 锁已被占用
            }
            
            // 设置锁
            redisCache.setCacheObject(lockKey, lockValue, (int)expireTime, timeUnit);
            
            // 验证设置是否成功
            String setValue = redisCache.getCacheObject(lockKey);
            return lockValue.equals(setValue);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 释放锁
     * @return 是否释放成功
     */
    public boolean unlock() {
        try {
            // 检查锁的值是否匹配
            String currentValue = redisCache.getCacheObject(lockKey);
            if (lockValue.equals(currentValue)) {
                return redisCache.deleteObject(lockKey);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查锁是否存在
     * @return 是否存在
     */
    public boolean isLocked() {
        try {
            return redisCache.getCacheObject(lockKey) != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取锁的值
     * @return 锁的值
     */
    public String getLockValue() {
        return lockValue;
    }
    
    /**
     * 静态方法：快速创建并尝试获取锁
     * @param lockKey 锁的键
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 锁对象，如果获取失败返回null
     */
    public static DistributedLock tryLock(String lockKey, long expireTime, TimeUnit timeUnit) {
        DistributedLock lock = new DistributedLock(lockKey);
        if (lock.tryLock(expireTime, timeUnit)) {
            return lock;
        }
        return null;
    }
}
