package com.yunmai.config;

import com.yunmai.util.PropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;

@Slf4j
@Configuration
public class PropertiesConfig {

    @Value("classpath:/config.properties")  // 通过类路径加载 config.properties 文件
    private Resource configResource;

    @Bean
    public PropertiesUtil propertiesUtil() {
        PropertiesUtil propertiesUtil = null;
        try {
            // 加载配置文件
            propertiesUtil = new PropertiesUtil(configResource.getInputStream());
        } catch (IOException e) {
            log.error("加载配置文件失败", e);
        }
        log.info("+++++++++++++ 初始化 配置文件 +++++++++++++++++");
        return propertiesUtil;
    }
}
