<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunmai.mapper.DaHuaPersonMapper">

    <resultMap type="CardTeachStudInfo" id="CardTeachStudInfoResult">
        <id property="uid" column="uid"/>

        <!-- 普通字段 -->
        <result property="code" column="code"/>
        <result property="card" column="card"/>
        <result property="name" column="name"/>
        <result property="infoType" column="infotype"/>
    </resultMap>


    <select id="selectAllPersonByCondition" resultType="com.yunmai.domain.CardTeachStudInfo">
        select uid, card, code, name ,infotype
        from tb_card_teachstudinfo
        <where>
            <!-- 判断 uid 是否为空 -->
            <if test="card != null and card != ''">
                card = #{card}
            </if>
        </where>
    </select>


    <select id="selectAllPerson" resultType="com.yunmai.domain.CardTeachStudInfo">
        select uid, card, code, name,infotype
        from tb_card_teachstudinfo
        <where>
            <!-- 固定条件 -->
            card != ''
            and card is not null
            and status = 1
        </where>

    </select>

</mapper>