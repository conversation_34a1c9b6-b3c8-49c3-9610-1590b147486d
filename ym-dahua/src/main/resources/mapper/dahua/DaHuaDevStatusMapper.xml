<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunmai.mapper.DaHuaDevStatusMapper">

    <resultMap type="Device" id="DeviceResult">
        <id property="uid" column="uid" />
        <result property="devSn" column="devsn" />
        <result property="machineId" column="machineid" />
        <result property="devName" column="devname" />
        <result property="devIp" column="devip" />
        <result property="devPort" column="devport" />
        <result property="serverUid" column="server_uid" />
        <result property="devStatus" column="devstatus" />
        <result property="livesTime" column="livestime" />
        <result property="devClass" column="devclass" />
        <result property="devModel" column="devmodel" />
        <result property="areaCode" column="areacode" />
        <result property="useClass" column="useclass" />
        <result property="used" column="used" />
        <result property="status" column="status" />
        <result property="createDate" column="createdate" />
        <result property="creatorId" column="creatorid" />
        <result property="modifyDate" column="modifydate" />
        <result property="modifierId" column="modifierid" />
        <result property="devToken" column="devtoken" />
    </resultMap>


    <update id="updateDevStatus">
        update tb_dev_accesscontroller
        <set>
            <if test="modifyDate != null and updateBy != ''">modifydate = #{modifyDate},</if>
            devstatus = #{devStatus},
            livestime = sysdate()
        </set>
        <where>
            <if test="devSn != null and devSn != ''">
                AND devsn =  #{devSn}
            </if>
            <if test="devClass != null and devClass != ''">
                AND devclass =  #{devClass}
            </if>
            <if test="devModel != null and devModel != ''">
                AND devmodel =  #{devModel}
            </if>
        </where>
    </update>

    <select id="selectAllByDevModelDeviceList" resultType="com.yunmai.domain.Device">
        select uid,machineid,used from tb_dev_accesscontroller
    </select>


    <select id="searchAllByDevSnDevice" resultType="com.yunmai.domain.Device">
        select uid,machineid,used from tb_dev_accesscontroller
        <where>
            <if test="machineId != null and machineId != ''">
                AND machineid =  #{machineId}
            </if>
        </where>
    </select>
</mapper>