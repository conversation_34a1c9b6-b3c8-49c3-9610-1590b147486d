<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunmai.mapper.DaHuaDownPersonMapper">

    <resultMap id="cardDtoResultMap" type="CardDto">
        <result property="cardNo" column="cardNo" />
        <result property="cardName" column="cardName" />
        <result property="userId" column="userId" />
        <result property="facePath" column="facePath" />
    </resultMap>

    <resultMap id="PersonDownResultMap" type="com.yunmai.domain.dto.PersonDownDto">
        <!-- 映射简单字段 -->
        <result property="deviceNo" column="deviceNo" />
        <result property="downMsg" column="downMsg" />
        <result property="faceDownStatus" column="faceDownStatus" />
        <result property="downStatus" column="downStatus" />
        <result property="downIndex" column="downIndex" />
        <result property="uid" column="uid" />
        <!-- 使用 association 进行关联查询 -->
        <association property="cardDto"  resultMap="cardDtoResultMap" />
    </resultMap>

    <delete id="deleteByCardNameInt">
       delete from tb_face_authorize_group_access where uid = #{uid}
    </delete>


    <!-- 查询人员下发信息 -->
    <select id="personDownResultMap" resultMap="PersonDownResultMap">
        SELECT
        ct.name AS cardName,
        ct.card AS cardNo,
        ct.code AS userId,
        fi.imgpath AS facePath,
        fa.record_no as downMsg,
        fa.downstatus as downStatus,
        fa.facedownstatus as faceDownStatus,
        fa.uid as uid,
        da.machineid as deviceNo,
        aw.weekindex as downIndex
        FROM
        tb_face_authorize_group_access fa
        JOIN
        tb_face_authorize_group fag
        ON fag.uid = fa.groupid
        JOIN
        tb_face_authorize_weekplan aw
        on aw.uid = fag.weekplanid
        JOIN
        tb_dev_accesscontroller da
        ON da.uid = fa.devid
        JOIN
        tb_card_teachstudinfo ct
        ON ct.uid = fa.infoid
        Left JOIN
        tb_face_infoface fi
        ON fi.infoid = ct.uid
        WHERE
        fa.downstatus IN
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>


    <update id="updateDownStatus">
        update tb_face_authorize_group_access
        <set>
            <if test="downMsg != null and downMsg != ''"> record_no = #{downMsg},</if>
            downstatus = #{downStatus},
            facedownstatus = #{faceDownStatus},
            downtime = sysdate()
        </set>
        <where>
            uid = #{uid}
        </where>
    </update>

</mapper>