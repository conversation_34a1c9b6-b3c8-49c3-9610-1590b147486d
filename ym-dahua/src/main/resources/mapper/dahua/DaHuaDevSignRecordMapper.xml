<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunmai.mapper.DaHuaDevSignRecordMapper">

    <resultMap type="FaceRecord" id="FaceRecordResult">
        <id property="uid" column="uid"/>

        <!-- 普通字段 -->
        <result property="infoId" column="infoid"/>
        <result property="devId" column="devid"/>
        <result property="machineId" column="machineid"/>
        <result property="facePath" column="facepath"/>
        <result property="faceDown" column="facedown"/>
        <result property="recordTime" column="recordtime"/>
        <result property="status" column="status"/>
        <result property="createDate" column="createdate"/>
        <result property="recordType" column="recordtype"/>
        <result property="tiWen" column="tiwen"/>
    </resultMap>

    <!-- 插入记录 -->
    <insert id="insertSignRecord" parameterType="com.yunmai.domain.SignRecord">
        INSERT INTO tb_sign_records
            (uid, signin_time, infoid, period_uid, status)
        VALUES
            (uuid(), #{signinTime}, #{infoId}, #{periodUid}, #{status});
    </insert>


    <!-- 查询记录 -->
    <select id="getSignRecordByCondition" parameterType="com.yunmai.domain.SignRecord" resultType="int">
        SELECT
            COUNT(1)
        FROM tb_sign_records
        WHERE
          period_uid = #{periodUid} and infoid = #{infoId} and DATE(signin_time) = CURDATE();
    </select>


    <select id="getSignPeriodUid" resultType="java.lang.String">

    </select>


    <select id="queryAllByParentInfoId" resultType="java.lang.String">
        select student_id from tb_card_parentinfo where parent_id = #{parentId}
    </select>


</mapper>