<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunmai.mapper.DaHuaDevRecordMapper">

    <resultMap type="FaceRecord" id="FaceRecordResult">
        <id property="uid" column="uid"/>

        <!-- 普通字段 -->
        <result property="infoId" column="infoid"/>
        <result property="devId" column="devid"/>
        <result property="machineId" column="machineid"/>
        <result property="facePath" column="facepath"/>
        <result property="faceDown" column="facedown"/>
        <result property="recordTime" column="recordtime"/>
        <result property="status" column="status"/>
        <result property="createDate" column="createdate"/>
        <result property="recordType" column="recordtype"/>
        <result property="tiWen" column="tiwen"/>
    </resultMap>


    <!-- 插入人脸记录 -->
    <insert id="insertFaceRecord" parameterType="com.yunmai.domain.FaceRecord">
        INSERT INTO tb_face_record (uid,
                                    infoid,
                                    devid,
                                    machineid,
                                    facepath,
                                    facedown,
                                    recordtime,
                                    status,
                                    createdate,
                                    recordtype,
                                    tiwen)
        VALUES (    #{uid},
                #{infoId},
                #{devId},
                #{machineId},
                #{facePath},
                #{faceDown},
                #{recordTime},
                #{status},
                now(),
                #{recordType},
                #{tiWen})
    </insert>

    <select id="queryByDevIdAndInfoIdInt" resultType="java.lang.Integer">
        select count(1)
        from tb_face_record
        <where>
            <!-- 判断 infoId 是否为空 -->
            <if test="uid != null and uid != ''">
                AND  infoid = #{uid}
            </if>
            <if test="machineId != null and machineId != ''">
                AND  machineid = #{machineId}
            </if>
            <if test="recordTime != null and recordTime != ''">
                AND  recordtime = #{recordTime}
            </if>
        </where>
    </select>
</mapper>