<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunmai.mapper.SignPeriodMapper">

    <!-- 查询当前时间所属的时段 UID -->
    <select id="getPeriodUidByTime" parameterType="java.util.Date" resultType="String">
        SELECT uid
        FROM tb_sign_periods
        WHERE
            DATE_FORMAT(#{currentTime}, '%H:%i') BETWEEN DATE_FORMAT(start_time, '%H:%i')
                AND DATE_FORMAT(end_time, '%H:%i')
            LIMIT 1
    </select>

</mapper>
