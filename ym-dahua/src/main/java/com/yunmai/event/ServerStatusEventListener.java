package com.yunmai.event;


import com.yunmai.callback.AnalyzerDataCBCallBack;
import com.yunmai.domain.Device;
import com.yunmai.mapper.DaHuaDevStatusMapper;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.module.GateModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * @description: 在线事件监听器，监听到离线后，修改设备离在线状态。 设备在线有两个来源：
 * 1、设备主动注销，发送注销指令
 * 2、设备未知原因离线，心跳超时
 */
@Component
public class ServerStatusEventListener {

    private final static Logger logger = LoggerFactory.getLogger(ServerStatusEventListener.class);

    public static final Map<NetSDKLib.LLong, String> deviceHandleMap = new ConcurrentHashMap<>();


    @Autowired
    private DaHuaDevStatusMapper daHuaDevStatusMapper;

    @Autowired
    private AnalyzerDataCBCallBack analyzerDataCBCallBack;



    @Async("taskExecutor")
    @EventListener
    public void onApplicationEvent(ServerOnlineEvent event) throws InterruptedException {
        logger.info("[大华设备节点] 待上线 ID：" + event.getMediaServerItemList().get(0).getId());
        //登录成功重新订阅
        beginSubscribe(event.getMediaServerItemList().get(0).getId());
    }


    @Async("taskExecutor")
    @EventListener
    public void onApplicationEvent(ServerOfflineEvent event) throws InterruptedException {
        int updateDevStatus = daHuaDevStatusMapper.updateDevStatus(new Device(0, event.getMediaServerId()));
        String result = updateDevStatus > 0 ? " 离线成功" : " 离线失败";
        logger.info("[大华设备节点] 离线 ID：" + event.getMediaServerId() + result);
    }

    /**
     * 默认启动设备离线
     */
    @PostConstruct
    public void initDevStatus() {
        Device device = new Device(0,"9","52");
        int updateDevStatus = daHuaDevStatusMapper.updateDevStatus(device);
        logger.info(updateDevStatus > 0 ? " 设备在线状态初始化成功" : " 设备在线状态初始化失败");
    }

//    /**
//     * 写成静态主要是防止被回收
//     */
//    private static class AnalyzerDataCB implements NetSDKLib.fAnalyzerDataCallBack {
//        private String devId;
//
//        private AnalyzerDataCB(String devId) {
//            this.devId = devId;
//        }
//
//        private AnalyzerDataCB() {
//        }
//
//        public static AnalyzerDataCB INSTANCE = new AnalyzerDataCB();
//
//        public static AnalyzerDataCB getInstance() {
//            return INSTANCE;
//        }
//
//        private BufferedImage gateBufferedImage = null;
//
//
//        public int invoke(
//                NetSDKLib.LLong lAnalyzerHandle,
//                int dwAlarmType,
//                Pointer pAlarmInfo,
//                Pointer pBuffer,
//                int dwBufSize,
//                Pointer dwUser,
//                int nSequence,
//                Pointer reserved) {
//            if (lAnalyzerHandle.longValue() == 0 || pAlarmInfo == null) {
//                return -1;
//            }
//            File path = new File("./GateSnapPicture/");
//            if (!path.exists()) {
//                path.mkdir();
//            }
//            switch (dwAlarmType) {
//
//                case NetSDKLib.EVENT_IVS_ACCESS_CTL:// /< 门禁事件
//                {
//                    NetSDKLib.DEV_EVENT_ACCESS_CTL_INFO msg = new NetSDKLib.DEV_EVENT_ACCESS_CTL_INFO();
//                    ToolKits.GetPointerData(pAlarmInfo, msg);
//                    // 获取门禁信息
//                    //打印msg中的卡信息和用戶信息
//                    int szCardNo = ByteBuffer.wrap(msg.szCardNo).getInt();
//                    logger.info("卡号：" + szCardNo);
//
//                    //设备号
//                    logger.info("设备号：" + devId);
//
//                    int szUserId = ByteBuffer.wrap(msg.szUserID).getInt();
//                    logger.info("十进制卡号：{}", szUserId);
//                    // 处理用户名信息
//                    String username = "";
//
//                    try {
//                        username = new String(msg.szCardNo, "GBK").trim();
//                    } catch (UnsupportedEncodingException e) {
//                        e.printStackTrace();
//                    }
//                    logger.info("卡号：" + username);
//
//                    if (StringUtils.isBlank(username)) {
//                        logger.info("远程开门成功");
//                        // 释放内存
//                        msg = null;
//                        System.gc();
//                        break;
//                    }
//
//                    int errorCode = msg.nErrorCode;
//
//                    if (errorCode == 96) {
//                        NetSDKLib.NET_CTRL_ACCESS_OPEN openInfo = new NetSDKLib.NET_CTRL_ACCESS_OPEN();
//                        openInfo.nChannelID = 0;
//                        openInfo.emOpenDoorType = NetSDKLib.EM_OPEN_DOOR_TYPE.EM_OPEN_DOOR_TYPE_REMOTE;
//                        Pointer pointer = new Memory(openInfo.size());
//                        ToolKits.SetStructDataToPointer(openInfo, pointer, 0);
//                        boolean ret = LoginModule.netsdk.CLIENT_ControlDeviceEx(LoginModule.loginAccessMap.get(devId).getM_hLoginHandle(),
//                                NetSDKLib.CtrlType.CTRLTYPE_CTRL_ACCESS_OPEN, pointer, null, 3000);
//                        if (!ret) {
//                            logger.info("远程开门失败！");
//                        }
//                    }
//
//                    // 保存图片，获取图片缓存
//                    String snapPicPath = path + "\\" + System.currentTimeMillis() + "GateSnapPicture.jpg";  // 保存图片地址
//                    byte[] buffer = pBuffer.getByteArray(0, dwBufSize);
//                    ByteArrayInputStream byteArrInputGlobal = new ByteArrayInputStream(buffer);
//
//                    try {
//                        gateBufferedImage = ImageIO.read(byteArrInputGlobal);
//                        if (gateBufferedImage != null) {
//                            ImageIO.write(gateBufferedImage, "jpg", new File(snapPicPath));
//                        }
//                    } catch (IOException e2) {
//                        e2.printStackTrace();
//                    }
//                    logger.info("门禁事件：卡号：{}，用户ID：{}", szCardNo, szUserId);
//                    // 释放内存
//                    msg = null;
//                    System.gc();
//                    break;
//
//                }
//                case NetSDKLib.EVENT_IVS_FACERECOGNITION: // /< 人脸识别事件
//                {
//
//                    // DEV_EVENT_FACERECOGNITION_INFO 结构体比较大，new对象会比较耗时， ToolKits.GetPointerData内容拷贝是不耗时的。
//                    // 如果多台设备或者事件处理比较频繁，可以考虑将 static DEV_EVENT_FACERECOGNITION_INFO msg = new
//                    // DEV_EVENT_FACERECOGNITION_INFO(); 改为全局。
//                    // 写成全局，是因为每次new花费时间较多, 如果改为全局，此case下的处理需要加锁
//                    // 加锁，是因为共用一个对象，防止数据出错
//
//                    NetSDKLib.DEV_EVENT_ACCESS_CTL_INFO msg = new NetSDKLib.DEV_EVENT_ACCESS_CTL_INFO();
//                    ToolKits.GetPointerData(pAlarmInfo, msg);
//                    // 获取门禁信息
//                    //打印msg中的卡信息和用戶信息
//                    int szCardNo = ByteBuffer.wrap(msg.szCardNo).getInt();
//                    System.out.println("卡号：" + szCardNo);
//                    int szUserId = ByteBuffer.wrap(msg.szUserID).getInt();
//                    System.out.printf("用户ID：%d\n", szUserId);
//
//                    // 保存图片，获取图片缓存
//                    String snapPicPath = path + "\\" + System.currentTimeMillis() + "GateSnapPicture.jpg";  // 保存图片地址
//                    byte[] buffer = pBuffer.getByteArray(0, dwBufSize);
//                    ByteArrayInputStream byteArrInputGlobal = new ByteArrayInputStream(buffer);
//
//                    try {
//                        gateBufferedImage = ImageIO.read(byteArrInputGlobal);
//                        if (gateBufferedImage != null) {
//                            ImageIO.write(gateBufferedImage, "jpg", new File(snapPicPath));
//                        }
//                    } catch (IOException e2) {
//                        e2.printStackTrace();
//                    }
//                    logger.info("人脸识别事件：卡号：{}，用户ID：{}", szCardNo, szUserId);
//                    // 释放内存
//                    msg = null;
//                    System.gc();
//
//                    break;
//                }
//                case NetSDKLib.EVENT_IVS_FACEDETECT: // /< 人脸检测
//                {
//                    NetSDKLib.DEV_EVENT_FACEDETECT_INFO msg = new NetSDKLib.DEV_EVENT_FACEDETECT_INFO();
//                    ToolKits.GetPointerData(pAlarmInfo, msg);
//                    //todo 保存图片，获取图片缓存
//
//                    // 释放内存
//                    msg = null;
//                    System.gc();
//                    break;
//                }
//                default:
//                    break;
//            }
//
//            return 0;
//        }
//    }

    @Autowired
    private ApplicationContext context;

    public void beginSubscribe(String deviceNo) {
        new Thread(() -> {
            AnalyzerDataCBCallBack callback = context.getBean(AnalyzerDataCBCallBack.class);
            NetSDKLib.LLong realLoadPic = GateModule.realLoadPic(deviceNo, 0, callback);

            if (realLoadPic != null && realLoadPic.longValue() != 0) {
                logger.info("SubscribeDevice{}{}", deviceNo, "订阅成功");
                deviceHandleMap.put(realLoadPic,deviceNo);
                int updateDevStatus = daHuaDevStatusMapper.updateDevStatus(new Device(1, deviceNo));
                String result = updateDevStatus > 0 ? " 在线成功" : " 在线失败";
                logger.info("[大华设备节点] 上线 ID：" + deviceNo + result);

            } else {
                logger.info("SubscribeDevice{}{}", deviceNo, "订阅失败");
            }
        }).start();
    }

    @Scheduled(fixedRate = 60000) // 每 60 秒检查一次
    public void checkSubscriptions() {
        for (Map.Entry<NetSDKLib.LLong,String> entry : deviceHandleMap.entrySet()) {
            NetSDKLib.LLong handle =entry.getKey();
            String deviceNo = entry.getValue();

            if (handle == null || handle.longValue() == 0 || !isHandleValid(handle)) {
                logger.warn("设备 {} 的订阅失效，尝试重新订阅", deviceNo);
                beginSubscribe(deviceNo);
            }
        }
    }

    public boolean isHandleValid(NetSDKLib.LLong handle) {
        // 根据实际 SDK 提供的函数判断句柄是否有效
        return handle != null && handle.longValue() != 0;
    }
}
