package com.yunmai.event;


import com.sun.jna.Pointer;

/**
 * 识别记录事件
 */
public class ServerDeviceRecordEvent extends ServerEventAbstract{

    private String cardNo;
    private String devId;
    private int dwAlarmType;
    private Pointer pAlarmInfo;
    private Pointer pBuffer;
    private int dwBufSize;

    public ServerDeviceRecordEvent(Object source) {
        super(source);
    }

    public ServerDeviceRecordEvent(Object source, String cardNo, String devId) {
        super(source);
        this.cardNo = cardNo;
        this.devId = devId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getDevId() {
        return devId;
    }

    public void setDevId(String devId) {
        this.devId = devId;
    }
}
