package com.yunmai.event;


import com.yunmai.domain.DeviceDto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 设备在线事件
 */
public class ServerOnlineEvent extends ServerEventAbstract {

	public ServerOnlineEvent(Object source) {
		super(source);
	}

	private List<DeviceDto> mediaServerItemList;

	public List<DeviceDto> getMediaServerItemList() {
		return mediaServerItemList;
	}

	public void setMediaServerItemList(List<DeviceDto> mediaServerItemList) {
		this.mediaServerItemList = mediaServerItemList;
	}

	public void setMediaServerItemList(DeviceDto... mediaServerItemArray) {
		this.mediaServerItemList = new ArrayList<>();
		this.mediaServerItemList.addAll(Arrays.asList(mediaServerItemArray));
	}

	public void setMediaServerItem(List<DeviceDto> mediaServerItemList) {
		this.mediaServerItemList = mediaServerItemList;
	}
}
