package com.yunmai.controller;

import cn.hutool.core.util.ObjectUtil;
import com.yunmai.common.core.domain.AjaxResult;
import com.yunmai.domain.AccessRecord;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.lib.ToolKits;
import com.yunmai.module.AutoRegisterModule;
import com.yunmai.module.LoginModule;
import com.yunmai.utils.AccessRecordUtils;
import com.yunmai.utils.FileUploadUtils;
import com.sun.jna.Memory;
import com.yunmai.domain.CardDto;
import com.yunmai.domain.Result;
import com.yunmai.service.CardService;
import com.yunmai.utils.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.yunmai.common.core.domain.AjaxResult.MSG_TAG;

/**
 * 卡管理控制器
 *
 * <AUTHOR> Xu
 * @version 1.0
 * @date 2021/8/6
 * Copyright © goodits
 */
@RestController
@RequestMapping("/card")
@Api("卡管理")
@Valid
public class CardController {

    @Resource
    private CardService cardService;
    @Resource
    private FileUploadUtils fileUploadUtils;
    @Resource
    private AccessRecordUtils accessRecordUtils;

    @PostMapping("/getList")
    @ApiOperation("获取卡列表")
    public Result<List<CardDto>> getList(@ApiParam(value = "设备唯一id", required = true) @NotBlank String deviceNo, @ApiParam("卡号") @RequestParam(value = "cardNo", required = false) String cardNo) {
        List<CardDto> list = cardService.getList(deviceNo, cardNo);
        return ResultUtil.data(list);
    }

    @PostMapping("/add")
    @ApiOperation("添加卡")
    public AjaxResult add(String deviceNo,
                      CardDto cardDto,
                      int timeIndex,
                      HttpServletRequest request) throws IOException {
        AjaxResult uploadResult = fileUploadUtils.upload(cardDto.getFace());
        if (Boolean.FALSE.equals(uploadResult.isSuccess())) {
            return AjaxResult.error("文件上传失败");
        }
        String picPath = (String) uploadResult.get(MSG_TAG);
        Memory memory = ToolKits.readPictureFile(picPath);
        //先发送请求到设备
        AjaxResult add = cardService.add(deviceNo,timeIndex, cardDto, memory);
//        //删除文件
//        fileUploadUtils.delete(picPath);
        return add;
    }

    @PostMapping("/update")
    @ApiOperation("修改卡")
    public AjaxResult update(String deviceNo,
                         CardDto cardDto,
                         int timeIndex,
                         HttpServletRequest request) throws IOException {
        Memory memory = null;
        String picPath = "";
        if (!ObjectUtil.isEmpty(cardDto.getFace())) {
            AjaxResult uploadResult = fileUploadUtils.upload(cardDto.getFace());
            if (Boolean.FALSE.equals(uploadResult.isSuccess())) {
                return AjaxResult.error("文件上传失败");
            }
            picPath = (String) uploadResult.get(MSG_TAG);
            memory = ToolKits.readPictureFile(picPath);
        }

        //先发送请求到设备
        AjaxResult update = cardService.update(deviceNo, timeIndex,cardDto, memory);
//        //再删除文件
//        fileUploadUtils.delete(picPath);
        return update;
    }

    @PostMapping("/updateFace")
    @ApiOperation("修改人脸照片")
    public AjaxResult updateFace(String deviceNo,
                             String userId,
                             MultipartFile face) throws IOException {
        AjaxResult uploadResult = fileUploadUtils.upload(face);
        if (Boolean.FALSE.equals(uploadResult.isSuccess())) {
            return AjaxResult.error("文件上传失败");
        }
        String picPath = (String) uploadResult.get(MSG_TAG);

        Memory memory = ToolKits.readPictureFile(picPath);
        //先发送请求到设备
        AjaxResult update = new AjaxResult();
//        AjaxResult update = cardService.updateFace(deviceNo, userId, memory);
//        //再删除文件
//        fileUploadUtils.delete(picPath);
        return update;
    }

    @PostMapping("/delete")
    @ApiOperation("删除人员信息")
    public AjaxResult delete(@ApiParam(value = "设备唯一id", required = true) @RequestParam @NotBlank String deviceNo,
                         @ApiParam(value = "记录集编号", required = true) @RequestParam @NotBlank String recordNo,
                         @ApiParam(value = "用户id", required = true) @RequestParam @NotBlank String userId) {
        return cardService.delete(deviceNo, recordNo, userId);
    }

    @PostMapping("/getAccessRecords")
    @ApiOperation("获取出入记录列表")
    public Result<List<AccessRecord>> getAccessRecords(@ApiParam(value = "设备唯一id", required = true) @RequestParam @NotBlank String deviceNo,
                                                       String cardNo,
                                                       @ApiParam(value = "起始时间") @RequestParam String startTime,
                                                       @ApiParam(value = "结束时间") @RequestParam String endTime) throws UnsupportedEncodingException {
        return accessRecordUtils.findRecords(deviceNo, cardNo, startTime, endTime);
    }


//    @PostMapping(value = "/upload")
//    public ResponseEntity<String> handleStream(HttpServletRequest request) {
//        // 打印请求的所有参数
//        Enumeration<String> parameterNames = request.getParameterNames();
//        while (parameterNames.hasMoreElements()) {
//            String paramName = parameterNames.nextElement();
//            System.out.println("Parameter Name: " + paramName + ", Value: " + request.getParameter(paramName));
//        }
//
//        // 处理上传逻辑
//        String cardNo = request.getParameter("cardNo");
//        System.out.println("Card Number: " + cardNo);
//
//        // 继续处理文件上传的逻辑
//        return ResponseEntity.ok("Parameters received successfully");
////        try (InputStream inputStream = request.getInputStream()) {
////            // 假设 boundary 是从请求头中获取的
////            String boundary = "myboundary"; // 需要从请求头中获得真实的 boundary
////            byte[] boundaryBytes = boundary.getBytes(StandardCharsets.UTF_8);
////
////            // 创建 MultipartStream 时需要提供缓冲区大小，通常是 4096 或其他合适的值
////            MultipartStream multipartStream = new MultipartStream(inputStream, boundaryBytes, 4096, null);
////
////            boolean nextPart = multipartStream.skipPreamble(); // 跳过 preamble 部分
////            while (nextPart) {
////                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
////                multipartStream.readBodyData(byteArrayOutputStream); // 读取当前部分的内容
////                byte[] partData = byteArrayOutputStream.toByteArray();
////                // 处理接收到的每一部分数据（例如图像、视频帧等）
////                System.out.println("Received part of size: " + partData.length);
////                nextPart = multipartStream.readBoundary(); // 读取下一个 boundary
////            }
////        } catch (IOException e) {
////            e.printStackTrace();
////            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error while processing stream");
////        }
////        System.out.println(cardNo);
////        return ResponseEntity.ok("Stream received");
//    }

    /**
     * 配置远程验证
     * openStatus 1为开启 0 为关闭
     */
    @PostMapping("/config")
    @ApiOperation("配置远程验证")
    public void remoteConfig(String devId,int openStatus) {
        NetSDKLib.LLong loginHandle = LoginModule.loginAccessMap.get(devId).getM_hLoginHandle();
        AutoRegisterModule.getEventConfigRemote(loginHandle,openStatus);
    }
}
