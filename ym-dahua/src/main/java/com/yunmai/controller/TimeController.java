package com.yunmai.controller;


import com.yunmai.common.core.domain.AjaxResult;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.lib.enumeration.ENUMERROR;
import com.yunmai.module.AutoRegisterModule;
import com.yunmai.module.LoginModule;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/time")
public class TimeController {

    //新增时段
    @PostMapping("/add")
    @ApiOperation("新增时段")
    public AjaxResult addTimeConfig(String devId) {
//        // 假设你已经登录了设备并获得了登录ID lLoginID
//        NetSDKLib.LLong lLoginID = LoginModule.loginAccessMap.get(devId).getM_hLoginHandle();  // 设备登录句柄
//        int nChannelID = 0;    // 通道号
//        int emCfgOpType = 100;  // 这里假设100是门禁时间段配置的操作类型，根据SDK文档调整
//        int waitTime = 5000;    // 超时时间 5秒
//        IntByReference restart = new IntByReference(0);  // 是否重启，0表示不需要重启
//        Pointer reServe = null; // 保留参数
//
//        // 创建门禁时间段配置对象
//        NetSDKLib.CFG_ACCESS_TIMESCHEDULE_INFO accessScheduleInfo = new NetSDKLib.CFG_ACCESS_TIMESCHEDULE_INFO();
//
//        // 设置是否启用时间段
//        accessScheduleInfo.bEnable = 1;  // 1表示启用时间段
//
//        // 设置时间段名称
//        String scheduleName = "工作时间";
//        System.arraycopy(scheduleName.getBytes(), 0, accessScheduleInfo.szName, 0, scheduleName.length());
//
//        // 配置每一天的时间段
//        // 例如：周一至周五设置相同的时间段
//        for (int i = 0; i < 7; i++) {
//            NetSDKLib.TIME_SECTION_WEEK_DAY_4 timeSection = new NetSDKLib.TIME_SECTION_WEEK_DAY_4();
//
//            // 设置周一到周五的时间段，每天最多4个时间段
//            for (int j = 0; j < 4; j++) {
//                NetSDKLib.CFG_TIME_SECTION time = new NetSDKLib.CFG_TIME_SECTION();
//                time.setStartTime(8, 0, 0);  // 设置开始时间 08:00:00
//                time.setEndTime(12, 0, 0);   // 设置结束时间 12:00:00
//
//                // 将设置好的时间段添加到该天的时间段中
//                timeSection.stuTimeSection[j] = time;
//            }
//
//            accessScheduleInfo.stuTimeWeekDay[i] = timeSection;
//        }

        NetSDKLib.LLong loginHandle = LoginModule.loginAccessMap.get(devId).getM_hLoginHandle();
        AutoRegisterModule.getEventTimeConfig(loginHandle);

//        // 将配置数据转换为 Pointer，并计算其大小
//        Pointer szInBuffer = accessScheduleInfo.getPointer();
//        int dwInBufferSize = accessScheduleInfo.size();  // 配置数据的大小

//        // 调用 CLIENT_SetConfig 设置门禁时间段CLIENT_SetNewDevConfig  CLIENT_SetConfig
//        boolean result = NetSDKLib.NETSDK_INSTANCE.CLIENT_SetNewDevConfig(lLoginID,CFG_CMD_ACCESSTIMESCHEDULE,0, szInBuffer, dwInBufferSize, waitTime, restart, reServe);
//
        AjaxResult add = new AjaxResult();
//        // 输出测试结果
//        if (result) {
//            System.out.println("刷卡时间段配置成功！"+ ToolKits.getErrorCodePrint());
//        } else {
//            System.out.println("刷卡时间段配置失败！"+ToolKits.getErrorCodePrint());
//        }
        return add;
    }

    /**
     * 开门
     * @return
     */
    public AjaxResult openDoor(String devId) {
        NetSDKLib.NET_CTRL_ACCESS_OPEN open = new NetSDKLib.NET_CTRL_ACCESS_OPEN();
        open.nChannelID = 0;
        open.write();
        String openStr = "";
        boolean openSuccess = NetSDKLib.CONFIG_INSTANCE.CLIENT_ControlDeviceEx(LoginModule.loginAccessMap.get(devId).getM_hLoginHandle(), NetSDKLib.CtrlType.CTRLTYPE_CTRL_ACCESS_OPEN, open.getPointer(), null, 3000);
        if (!openSuccess) {
            openStr = "开门失败，sdk错误码："+ ENUMERROR.getErrorCode()+"错误信息"+ENUMERROR.getErrorMessage();
//            return JsonData.buildError("开门失败，sdk错误码："+ENUMERROR.getErrorCode()+"错误信息"+ENUMERROR.getErrorMessage());
        }else {
            openStr = "开门成功";
        }
        System.out.println(openStr);
        open.read();
        AjaxResult add = new AjaxResult();
        return add;
    }

    /**
     * 配置远程验证
     * openStatus 1为开启 0 为关闭
     */
    //新增时段
    @PostMapping("/config")
    @ApiOperation("新增时段")
    public void remoteConfig(String devId,int openStatus) {
        NetSDKLib.LLong loginHandle = LoginModule.loginAccessMap.get(devId).getM_hLoginHandle();
        openStatus = 1;
        AutoRegisterModule.getEventConfigRemote(loginHandle,openStatus);
    }
}
