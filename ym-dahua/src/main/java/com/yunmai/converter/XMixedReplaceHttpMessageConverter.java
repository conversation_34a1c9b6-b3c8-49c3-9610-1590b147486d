package com.yunmai.converter;

import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.AbstractHttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;

import java.io.IOException;

public class XMixedReplaceHttpMessageConverter extends AbstractHttpMessageConverter<Object> {

    public XMixedReplaceHttpMessageConverter() {
        super(new MediaType("multipart", "x-mixed-replace"));
    }

    @Override
    protected boolean supports(Class<?> aClass) {
        return true;
    }

    @Override
    protected Object readInternal(Class<?> aClass, HttpInputMessage httpInputMessage) throws IOException, HttpMessageNotReadableException {
        return httpInputMessage.getBody();
    }

    @Override
    protected void writeInternal(Object o, HttpOutputMessage httpOutputMessage) throws IOEx<PERSON>, HttpMessageNotWritableException {
        httpOutputMessage.getBody().write((byte[]) o);
    }
}
