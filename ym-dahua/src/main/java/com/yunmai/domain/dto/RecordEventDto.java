package com.yunmai.domain.dto;


import com.alibaba.fastjson2.annotation.JSO<PERSON>ield;

import java.util.Date;

public class RecordEventDto {
    private int usedType;
    @J<PERSON><PERSON>ield(name = "Uid")
    private String uid;
    @<PERSON><PERSON><PERSON>ield(name = "Machineid")
    private String machineId;
    @JSO<PERSON>ield(name = "Readhead")
    private int readHead;
    @JSO<PERSON>ield(name = "Recordtime")
    private Date recordTime;
    @JSO<PERSON>ield(name = "Infoid")
    private String infoId;
    @<PERSON>SO<PERSON>ield(name = "Persontype")
    private int personType;

    public int getUsedType() {
        return usedType;
    }

    public void setUsedType(int usedType) {
        this.usedType = usedType;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMachineId() {
        return machineId;
    }

    public void setMachineId(String machineId) {
        this.machineId = machineId;
    }

    public int getReadHead() {
        return readHead;
    }

    public void setReadHead(int readHead) {
        this.readHead = readHead;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    public int getPersonType() {
        return personType;
    }

    public void setPersonType(int personType) {
        this.personType = personType;
    }
}
