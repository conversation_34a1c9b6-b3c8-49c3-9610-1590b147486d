package com.yunmai.domain.dto;


import com.yunmai.lib.NetSDKLib;

/**
 * 设备信息保存
 */
public class LoginAccessDto {
    //设备信息
    private NetSDKLib.NET_DEVICEINFO_Ex m_stDeviceInfo;

    //登录句柄
    private NetSDKLib.LLong m_hLoginHandle;

    //订阅句柄
    private NetSDKLib.LLong m_hAttachHandle;

    public LoginAccessDto(NetSDKLib.NET_DEVICEINFO_Ex m_stDeviceInfo, NetSDKLib.LLong m_hLoginHandle,NetSDKLib.LLong m_hAttachHandle) {
        this.m_stDeviceInfo = m_stDeviceInfo;
        this.m_hLoginHandle = m_hLoginHandle;
        this.m_hAttachHandle = m_hAttachHandle;
    }
    public LoginAccessDto(NetSDKLib.NET_DEVICEINFO_Ex m_stDeviceInfo, NetSDKLib.LLong m_hLoginHandle) {
        this.m_stDeviceInfo = m_stDeviceInfo;
        this.m_hLoginHandle = m_hLoginHandle;
    }
    public NetSDKLib.NET_DEVICEINFO_Ex getM_stDeviceInfo() {
        return m_stDeviceInfo;
    }

    public void setM_stDeviceInfo(NetSDKLib.NET_DEVICEINFO_Ex m_stDeviceInfo) {
        this.m_stDeviceInfo = m_stDeviceInfo;
    }

    public NetSDKLib.LLong getM_hAttachHandle() {
        return m_hAttachHandle;
    }

    public void setM_hAttachHandle(NetSDKLib.LLong m_hAttachHandle) {
        this.m_hAttachHandle = m_hAttachHandle;
    }

    public NetSDKLib.LLong getM_hLoginHandle() {
        return m_hLoginHandle;
    }

    public void setM_hLoginHandle(NetSDKLib.LLong m_hLoginHandle) {
        this.m_hLoginHandle = m_hLoginHandle;
    }
}
