package com.yunmai.domain;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 门禁卡对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/6
 * Copyright © goodits
 */
@ApiModel("门禁卡")
public class CardDto {

    @ApiModelProperty(value = "序号",hidden = true)
    private String serialNumber;

    @ApiModelProperty("卡号")
    private String cardNo;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("卡名")
    private String cardName;

    @ApiModelProperty("卡密码")
    private String cardPasswd;

    @ApiModelProperty("卡状态[-1:未知|0:正常|1:挂失|2:注销|3:冻结|4:欠费|5:逾期|6:预欠费]")
    private Integer cardStatus;

    @ApiModelProperty(value = "卡状态名称",hidden = true)
    private String cardStatusName;

    @ApiModelProperty("卡类型[-1:未知|0:一般卡|1:VIP卡|2:来宾卡|3:巡逻卡|4:黑名单卡|5:胁迫卡|6:巡检卡|7:母卡]")
    private Integer cardType;

    @ApiModelProperty(value = "卡类型名称",hidden = true)
    private String cardTypeName;

    @ApiModelProperty("使用次数，仅当卡类型为来宾卡时有效")
    private String useTimes;

    @ApiModelProperty("是否首卡[0:不是|1:是]")
    private Integer firstEnter;

    @ApiModelProperty("是否有效[0:不是|1:是]")
    private Integer enable;

    @ApiModelProperty("有效期开始时间")
    private String startTime;

    @ApiModelProperty("有效期结束时间")
    private String endTime;

    @ApiModelProperty("记录集编号， 修改、删除卡信息必须填写")
    private Integer recordNo;

    @ApiModelProperty("人脸照片")
    private MultipartFile face;

    /**
     * 照片路径
     */
    private String facePath;

    public String getFacePath() {
        return facePath;
    }

    public void setFacePath(String facePath) {
        this.facePath = facePath;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

//    public String getCardNo() {
//        return cardNo;
//    }

    public String getCardNo() {
        if (cardNo == null || cardNo.isEmpty()) {
            return null; // 如果卡号为空，直接返回
        }
        try {
            // 假设卡号是十进制字符串，先转换为 Long
            long cardNoDecimal = Long.parseLong(cardNo);

            // 转为16进制字符串（补全到8位，不足则填充0）
            String hexCardNo = String.format("%08X", cardNoDecimal).substring(0, 8);

            // 转换为大小端（每两位交换位置）
            StringBuilder reversedHex = new StringBuilder();
            for (int i = hexCardNo.length(); i > 0; i -= 2) {
                reversedHex.append(hexCardNo, i - 2, i);
            }

            return reversedHex.toString();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("卡号格式不正确，无法转换：" + cardNo, e);
        }
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCardPasswd() {
        return cardPasswd;
    }

    public void setCardPasswd(String cardPasswd) {
        this.cardPasswd = cardPasswd;
    }

    public Integer getCardStatus() {
        if (cardStatus == null) {
            return cardStatus = 0;
        }
        return cardStatus;
    }

    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    public String getCardStatusName() {
        return cardStatusName;
    }

    public void setCardStatusName(String cardStatusName) {
        this.cardStatusName = cardStatusName;
    }

    public Integer getCardType() {
        if (cardType == null) {
            return cardType = 0;
        }
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getCardTypeName() {
        return cardTypeName;
    }

    public void setCardTypeName(String cardTypeName) {
        this.cardTypeName = cardTypeName;
    }

    public String getUseTimes() {
        return useTimes;
    }

    public void setUseTimes(String useTimes) {
        this.useTimes = useTimes;
    }

    public Integer getFirstEnter() {
        if (firstEnter == null) {
            return firstEnter = 0;
        }
        return firstEnter;
    }

    public void setFirstEnter(Integer firstEnter) {
        this.firstEnter = firstEnter;
    }

    public Integer getEnable() {
        if (enable == null) {
            return enable = 1;
        }
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String getStartTime() {
        if (startTime == null) {
            return startTime = "2024-11-01 00:00:00";
        }
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        if (endTime == null) {
            return endTime = "2050-11-01 00:00:00";
        }
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(Integer recordNo) {
        this.recordNo = recordNo;
    }

    public MultipartFile getFace() {
        return face;
    }

    public void setFace(MultipartFile face) {
        this.face = face;
    }


}
