package com.yunmai.domain;

import java.util.Date;

/**
 * 设备对象
 */
public class Device {

    /**
     * 设备唯一标识符
     */
    private String uid;

    /**
     * 设备序列号
     */
    private String devSn;

    /**
     * 机器编号
     */
    private String machineId;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备IP地址
     */
    private String devIp;

    /**
     * 设备端口号
     */
    private Integer devPort;

    /**
     * 服务器唯一标识符
     */
    private String serverUid;

    /**
     * 设备状态
     */
    private int devStatus;

    /**
     * 最近在线时间
     */
    private Date livesTime;

    /**
     * 设备类型 9 代表 人脸识别
     */
    private String devClass;

    /**
     * 设备型号 52 代表大华设备
     */
    private String devModel;

    /**
     * 区域代码
     */
    private String areaCode;

    /**
     * 使用类别
     */
    private String useClass;

    /**
     * 使用状态 3 出入学校 28家长签到
     */
    private String used;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建者ID
     */
    private String creatorId;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 修改者ID
     */
    private String modifierId;

    /**
     * 设备认证令牌
     */
    private String devToken;

    public Device() {
    }

    public Device(int devStatus,String devId) {
        this.devSn = devId;
        this.devStatus = devStatus;
    }

    public Device(int devStatus, String devClass, String devModel) {
        this.devClass = devClass;
        this.devStatus = devStatus;
        this.devModel = devModel;
    }

    // Getter 和 Setter 方法
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getDevSn() {
        return devSn;
    }

    public void setDevSn(String devSn) {
        this.devSn = devSn;
    }

    public String getMachineId() {
        return machineId;
    }

    public void setMachineId(String machineId) {
        this.machineId = machineId;
    }

    public String getDevName() {
        return devName;
    }

    public void setDevName(String devName) {
        this.devName = devName;
    }

    public String getDevIp() {
        return devIp;
    }

    public void setDevIp(String devIp) {
        this.devIp = devIp;
    }

    public Integer getDevPort() {
        return devPort;
    }

    public void setDevPort(Integer devPort) {
        this.devPort = devPort;
    }

    public String getServerUid() {
        return serverUid;
    }

    public void setServerUid(String serverUid) {
        this.serverUid = serverUid;
    }

    public int getDevStatus() {
        return devStatus;
    }

    public void setDevStatus(int devStatus) {
        this.devStatus = devStatus;
    }

    public Date getLiveTime() {
        return livesTime;
    }

    public void setLiveTime(Date livesTime) {
        this.livesTime = livesTime;
    }

    public String getDevClass() {
        return devClass;
    }

    public void setDevClass(String devClass) {
        this.devClass = devClass;
    }

    public String getDevModel() {
        return devModel;
    }

    public void setDevModel(String devModel) {
        this.devModel = devModel;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getUseClass() {
        return useClass;
    }

    public void setUseClass(String useClass) {
        this.useClass = useClass;
    }

    public String getUsed() {
        return used;
    }

    public void setUsed(String used) {
        this.used = used;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getModifierId() {
        return modifierId;
    }

    public void setModifierId(String modifierId) {
        this.modifierId = modifierId;
    }

    public String getDevToken() {
        return devToken;
    }

    public void setDevToken(String devToken) {
        this.devToken = devToken;
    }
}
