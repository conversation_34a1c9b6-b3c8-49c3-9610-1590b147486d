package com.yunmai.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("出入记录")
public class AccessRecord {
    @ApiModelProperty("记录集编号,只读")
    public int recordNo;                                 // 记录集编号,只读
    @ApiModelProperty("卡号")
    public String cardNo;// 卡号
    @ApiModelProperty("卡名称")
    public String cardName;// 卡名称
    @ApiModelProperty("刷卡时间")
    public String stuTime;                                // 刷卡时间
    @ApiModelProperty("刷卡结果")
    public int bStatus;                                // 刷卡结果,TRUE表示成功,FALSE表示失败
    @ApiModelProperty("开门方式")
    public int emMethod;                                // 开门方式 NET_ACCESS_DOOROPEN_METHOD
    @ApiModelProperty("门号")
    public int door;                                  // 门号,即CFG_CMD_ACCESS_EVENT配置CFG_ACCESS_EVENT_INFO的数组下标
    @ApiModelProperty("用户ID")
    public String userId; // 用户ID
    @ApiModelProperty("开锁抓拍上传的FTP地址")
    public String snapFtpUrl;        // 开锁抓拍上传的FTP地址
    @ApiModelProperty("读卡器ID")
    public String readerID;// 读卡器ID													// 开门并上传抓拍照片,在记录集记录存储地址,成功才有
    @ApiModelProperty("卡类型")
    public int cardType;                            // 卡类型 NET_ACCESSCTLCARD_TYPE
    @ApiModelProperty("开门失败的原因")
    public int errorCode;                            // 开门失败的原因,仅在bStatus为FALSE时有效

    public int getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(int recordNo) {
        this.recordNo = recordNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getStuTime() {
        return stuTime;
    }

    public void setStuTime(String stuTime) {
        this.stuTime = stuTime;
    }

    public int getBStatus() {
        return bStatus;
    }

    public void setBStatus(int bStatus) {
        this.bStatus = bStatus;
    }

    public int getEmMethod() {
        return emMethod;
    }

    public void setEmMethod(int emMethod) {
        this.emMethod = emMethod;
    }

    public int getDoor() {
        return door;
    }

    public void setDoor(int door) {
        this.door = door;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSnapFtpUrl() {
        return snapFtpUrl;
    }

    public void setSnapFtpUrl(String snapFtpUrl) {
        this.snapFtpUrl = snapFtpUrl;
    }

    public String getReaderID() {
        return readerID;
    }

    public void setReaderID(String readerID) {
        this.readerID = readerID;
    }

    public int getCardType() {
        return cardType;
    }

    public void setCardType(int cardType) {
        this.cardType = cardType;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }
}