package com.yunmai.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 人脸闸机设备
 */
@ApiModel("人脸闸机设备")
public class DeviceDto {
    @ApiModelProperty("设备唯一id")
    private String id;
    @ApiModelProperty("IP地址")
    private String m_strIp;
    @ApiModelProperty("端口号")
    private Integer m_nPort;
    @ApiModelProperty("用户名")
    private String m_strUser;
    @ApiModelProperty("密码")
    private String m_strPassword;

    public DeviceDto(String id, String m_strIp, Integer m_nPort, String m_strUser, String m_strPassword) {
        this.id = id;
        this.m_strIp = m_strIp;
        this.m_nPort = m_nPort;
        this.m_strUser = m_strUser;
        this.m_strPassword = m_strPassword;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getM_strIp() {
        return m_strIp;
    }

    public void setM_strIp(String m_strIp) {
        this.m_strIp = m_strIp;
    }

    public Integer getM_nPort() {
        return m_nPort;
    }

    public void setM_nPort(Integer m_nPort) {
        this.m_nPort = m_nPort;
    }

    public String getM_strUser() {
        return m_strUser;
    }

    public void setM_strUser(String m_strUser) {
        this.m_strUser = m_strUser;
    }

    public String getM_strPassword() {
        return m_strPassword;
    }

    public void setM_strPassword(String m_strPassword) {
        this.m_strPassword = m_strPassword;
    }
}
