package com.yunmai.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 人脸记录表对象
 */
public class FaceRecord {
    /** 主键，唯一标识符 */
    private String uid;

    /** 信息ID */
    private String infoId;

    /** 设备ID */
    private String devId;

    /** 设备编号 */
    private String machineId;

    /** 人脸图片路径 */
    private String facePath;

    /** 人脸数据是否下载，0未下载，1已下载 */
    private Integer faceDown;

    /** 记录时间 */
    private Date recordTime;

    /** 状态 */
    private Integer status;

    /** 创建日期 */
    private Date createDate;

    /** 记录类型 */
    private Integer recordType;

    /** 体温 */
    private BigDecimal tiWen;

    // Getters and Setters
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    public String getDevId() {
        return devId;
    }

    public void setDevId(String devId) {
        this.devId = devId;
    }

    public String getMachineId() {
        return machineId;
    }

    public void setMachineId(String machineId) {
        this.machineId = machineId;
    }

    public String getFacePath() {
        return facePath;
    }

    public void setFacePath(String facePath) {
        this.facePath = facePath;
    }

    public Integer getFaceDown() {
        return faceDown;
    }

    public void setFaceDown(Integer faceDown) {
        this.faceDown = faceDown;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getRecordType() {
        return recordType;
    }

    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }

    public BigDecimal getTiWen() {
        return tiWen;
    }

    public void setTiWen(BigDecimal tiWen) {
        this.tiWen = tiWen;
    }
}
