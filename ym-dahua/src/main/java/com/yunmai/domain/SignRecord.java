package com.yunmai.domain;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 签到记录表实体类
 */
public class SignRecord {

    /**
     * 主键 UID
     */
    private String uid;

    /**
     * 签到时间
     */
    private Date signinTime;

    /**
     * 家长 ID
     */
    private String infoId;

    /**
     * 时段表 ID
     */
    private String periodUid;

    /**
     * 状态 (0 代表未离校, 1 代表已离校)
     */
    private Integer status;

    // Getter 和 Setter 方法

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getSigninTime() {
        return signinTime;
    }

    public void setSigninTime(Date signinTime) {
        this.signinTime = signinTime;
    }

    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    public String getPeriodUid() {
        return periodUid;
    }

    public void setPeriodUid(String periodUid) {
        this.periodUid = periodUid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
