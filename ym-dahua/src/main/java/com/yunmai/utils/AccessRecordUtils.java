package com.yunmai.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sun.jna.Pointer;
import com.yunmai.callback.AnalyzerDataCBCallBack;
import com.yunmai.common.config.RuoYiConfig;
import com.yunmai.common.core.redis.RedisCache;
import com.yunmai.common.utils.uuid.UUID;
import com.yunmai.constants.DaHuaConstants;
import com.yunmai.domain.AccessRecord;
import com.yunmai.domain.CardTeachStudInfo;
import com.yunmai.domain.Device;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.lib.ToolKits;
import com.yunmai.common.utils.StringUtils;
import com.sun.jna.Memory;
import com.yunmai.domain.Result;
import com.yunmai.mapper.DaHuaDevRecordMapper;
import com.yunmai.mapper.DaHuaDevStatusMapper;
import com.yunmai.mapper.DaHuaPersonMapper;
import com.yunmai.module.LoginModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 出入记录工具类
 *
 * <AUTHOR> Xu
 * @version 1.0
 * @date 2021/8/7
 * Copyright © goodits
 */
@Component
public class AccessRecordUtils {
    private static final String charSet = "GBK";

    private final Logger logger = LoggerFactory.getLogger(AccessRecordUtils.class);

    private static String convertToStr(byte[] bytes) throws UnsupportedEncodingException {
        return new String(bytes, charSet).trim();
    }

    @Autowired
    private AnalyzerDataCBCallBack analyzerDataCBCallBack;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DaHuaPersonMapper daHuaPersonMapper;

    @Autowired
    private DaHuaDevStatusMapper daHuaDevStatusMapper;

    @Autowired
    private DaHuaDevRecordMapper daHuaDevRecordMapper;


    public Result<List<AccessRecord>> findRecords(String deviceNo, String cardNo, String startTime, String endTime) throws UnsupportedEncodingException {
        NetSDKLib.NET_TIME startNetTime = getNetTime(startTime);
        NetSDKLib.NET_TIME endNetTime = getNetTime(endTime);
        NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC[] records = findRecords(cardNo, startNetTime, endNetTime,deviceNo);
        List<AccessRecord> accessRecordList = new LinkedList<>();
        for (NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC record : records) {
            AccessRecord accessRecord = new AccessRecord();
            String userId = convertToStr(record.szUserID);
            accessRecord.setRecordNo(record.nRecNo);
            accessRecord.setCardNo(convertToStr(record.szCardNo));
            accessRecord.setCardName(convertToStr(record.szCardName));
            accessRecord.setUserId(userId);
            accessRecord.setCardType(record.emCardType);
            accessRecord.setReaderID(convertToStr(record.szReaderID));
            accessRecord.setDoor(record.nDoor);
            accessRecord.setBStatus(record.bStatus);
            accessRecord.setErrorCode(record.nErrorCode);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd H:m:s");
            String zTime = record.stuTime.toStringTimeEx();
            // 解析 UTC 时间为 ZonedDateTime
            ZonedDateTime utcTime = ZonedDateTime.parse(zTime, formatter.withZone(ZoneId.of("UTC")));

            // 将 UTC 时间转换为本地时间
            ZonedDateTime localDateTime = utcTime.withZoneSameInstant(ZoneId.systemDefault()); // 转换为本地时区
            String recordTime = localDateTime.format(formatter);
            accessRecord.setStuTime(recordTime);
            accessRecord.setEmMethod(record.emMethod);
            Date dateTime = Date.from(localDateTime.toInstant());
            if (record.szSnapFtpUrl != null) {
                String remoteFilePath = convertToStr(record.szSnapFtpUrl);// 设备中的文件路径
                accessRecord.setSnapFtpUrl(convertToStr(record.szSnapFtpUrl));
                //保存到指定的数据中
                // 下载远程文件
                //异步下载先判断是否存在

                CompletableFuture.runAsync(() -> {
                    String yearMonth = localDateTime.format(DateTimeFormatter.ofPattern("yyyyMM"));
                    String day = localDateTime.format(DateTimeFormatter.ofPattern("dd"));
                    String downPathImg = "/recordimg/" + yearMonth + "/" + day;
                    //判断路径是否存在
                    File dir = new File(RuoYiConfig.getProfile() + downPathImg);
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }
                    String uid = UUID.fastUUID().toString();
                    String localFilePath = downPathImg+"/" + uid + ".jpg";             // 保存到本地的路径
                    CardTeachStudInfo cardTeachStudInfo = redisCache.getCacheObject(DaHuaConstants.PERSON_KEY  + userId);
                    if (ObjectUtil.isEmpty(cardTeachStudInfo)) {
                        CardTeachStudInfo cardTeachStudCache = new CardTeachStudInfo();
                        cardTeachStudCache.setCode(userId);
                        //从数据库中获取存入缓存
                        try {
                            cardTeachStudInfo = daHuaPersonMapper.selectAllPersonByCondition(cardTeachStudCache);
                        } catch (Exception e) {
                            e.printStackTrace();
//                            break;
                        }
                        if (!ObjectUtil.isEmpty(cardTeachStudInfo)) {
                            String personCacheKey = DaHuaConstants.PERSON_KEY +  cardTeachStudInfo.getCode();
                            redisCache.setCacheObject(personCacheKey, cardTeachStudInfo, 120, TimeUnit.DAYS);
                        }
                    }

                    Device device = redisCache.getCacheObject(DaHuaConstants.DEVICE_KEY + deviceNo);
                    if (ObjectUtil.isEmpty(device)) {
                        Device deviceCache = new Device();
                        deviceCache.setMachineId(deviceNo);
                        //从数据库中获取存入缓存
                        try {
                            device = daHuaDevStatusMapper.searchAllByDevSnDevice(deviceCache);
                        } catch (Exception e) {
                            e.printStackTrace();
//                            break;
                        }
                        if (!ObjectUtil.isEmpty(device)) {
                            String personCacheKey = DaHuaConstants.DEVICE_KEY + device.getMachineId();
                            redisCache.setCacheObject(personCacheKey, device, 1, TimeUnit.DAYS);
                        }
                    }

                    int existInt = daHuaDevRecordMapper.queryByDevIdAndInfoIdInt(cardTeachStudInfo.getUid(), deviceNo, recordTime);
                    if (existInt == 0) {
                        logger.info("保存成功,人员:{},设备:{}", userId, deviceNo);
                        //保存到数据库中
                        analyzerDataCBCallBack.handleAccessEvent(dateTime,downPathImg,device.getUid(),cardTeachStudInfo.getUid(),deviceNo,1,UUID.fastUUID().toString());
                    }

                    //保存图片
                    // 设置输入参数
                    NetSDKLib.NET_IN_DOWNLOAD_REMOTE_FILE inParam = new NetSDKLib.NET_IN_DOWNLOAD_REMOTE_FILE();
                    inParam.pszFileName = new Memory(remoteFilePath.length() + 1);
                    inParam.pszFileName.setString(0, remoteFilePath);

                    inParam.pszFileDst = new Memory((RuoYiConfig.getProfile()+localFilePath).length() + 1);
                    inParam.pszFileDst.setString(0, RuoYiConfig.getProfile()+localFilePath);

                    // 设置输出参数
                    NetSDKLib.NET_OUT_DOWNLOAD_REMOTE_FILE outParam = new NetSDKLib.NET_OUT_DOWNLOAD_REMOTE_FILE();
                    outParam.dwMaxFileBufLen = 10 * 1024 * 1024; // 分配 10MB 缓冲区
                    outParam.pstFileBuf = new Memory(outParam.dwMaxFileBufLen);

                    // 下载文件
                    boolean result = NetSDKLib.NETSDK_INSTANCE.CLIENT_DownloadRemoteFile(LoginModule.loginAccessMap.get(deviceNo).getM_hLoginHandle(), inParam, outParam, 5000); // 5秒超时
                    if (!result) {
                        logger.info("文件下载失败，错误码：" + ToolKits.getErrorCodePrint());
                    } else {
                        logger.info("文件下载成功，文件大小：" + outParam.dwRetFileBufLen + " 字节");
                        // 将缓冲区中的文件数据写入本地文件
                        saveToFile(outParam.pstFileBuf, outParam.dwRetFileBufLen, RuoYiConfig.getProfile()+localFilePath);
                    }
                });
            }
            accessRecordList.add(accessRecord);
        }
        return ResultUtil.data(accessRecordList);
    }

    // 将下载的文件数据写入本地文件
    private static void saveToFile(Pointer buffer, int length, String filePath) {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            byte[] data = buffer.getByteArray(0, length);
            fos.write(data);
            System.out.println("文件保存成功：" + filePath);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("文件保存失败！");
        }
    }

    private NetSDKLib.NET_TIME getNetTime(String startTime) {
        NetSDKLib.NET_TIME netTime = null;
        if (StringUtils.isNotBlank(startTime)) {
            LocalDateTime startDateTime = DateUtil.parseLocalDateTime(startTime);
            netTime = new NetSDKLib.NET_TIME();
            netTime.setTime(startDateTime.getYear(), startDateTime.getMonthValue(), startDateTime.getDayOfMonth(), startDateTime.getHour(), startDateTime.getMinute(), startDateTime.getSecond());
        }
        return netTime;
    }

    /**
     * 查询刷卡记录，获取查询句柄
     *
     * @return
     */
    private static NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC[] findRecords(String cardNo, NetSDKLib.NET_TIME startTime, NetSDKLib.NET_TIME endTime,String devId) {
        // 接口入参
        NetSDKLib.FIND_RECORD_ACCESSCTLCARDREC_CONDITION_EX findCondition = new NetSDKLib.FIND_RECORD_ACCESSCTLCARDREC_CONDITION_EX();
        //设置是否支持卡号查询
        findCondition.bCardNoEnable = 0;
        if (StrUtil.isNotBlank(cardNo)) {
            findCondition.bCardNoEnable = 1;
            findCondition.szCardNo = cardNo.getBytes();
        }

        //设置是否支持时间区间查询
        findCondition.bTimeEnable = 0;
        if (ObjectUtil.isNotEmpty(startTime) && ObjectUtil.isNotEmpty(endTime)) {
            findCondition.bTimeEnable = 1;
            findCondition.stStartTime = startTime;
            findCondition.stEndTime = endTime;
        }

        // CLIENT_FindRecord 接口入参
        NetSDKLib.NET_IN_FIND_RECORD_PARAM stIn = new NetSDKLib.NET_IN_FIND_RECORD_PARAM();
        stIn.emType = NetSDKLib.EM_NET_RECORD_TYPE.NET_RECORD_ACCESSCTLCARDREC_EX;
        stIn.pQueryCondition = findCondition.getPointer();

        // CLIENT_FindRecord 接口出参
        NetSDKLib.NET_OUT_FIND_RECORD_PARAM stOut = new NetSDKLib.NET_OUT_FIND_RECORD_PARAM();
        findCondition.write();

        NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC[] pstRecordEx = new NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC[0];
        // 获取查询句柄
        if (LoginModule.netsdk.CLIENT_FindRecord(LoginModule.loginAccessMap.get(devId).getM_hLoginHandle(), stIn, stOut, 5000)) {
            findCondition.read();

            // 用于申请内存，假定2000次刷卡记录
            int nFindCount = 2000;
            NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC[] pstRecord = new NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC[nFindCount];
            for (int i = 0; i < nFindCount; i++) {
                pstRecord[i] = new NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC();
            }

            NetSDKLib.NET_IN_FIND_NEXT_RECORD_PARAM stNextIn = new NetSDKLib.NET_IN_FIND_NEXT_RECORD_PARAM();
            stNextIn.lFindeHandle = stOut.lFindeHandle;
            stNextIn.nFileCount = nFindCount;

            NetSDKLib.NET_OUT_FIND_NEXT_RECORD_PARAM stNextOut = new NetSDKLib.NET_OUT_FIND_NEXT_RECORD_PARAM();
            stNextOut.nMaxRecordNum = nFindCount;
            // 申请内存
            stNextOut.pRecordList = new Memory((long) pstRecord[0].dwSize * nFindCount);
            stNextOut.pRecordList.clear((long) pstRecord[0].dwSize * nFindCount);

            // 将数组内存拷贝给指针
            ToolKits.SetStructArrToPointerData(pstRecord, stNextOut.pRecordList);

            if (LoginModule.netsdk.CLIENT_FindNextRecord(stNextIn, stNextOut, 5000)) {
                System.err.println("stNextOut.nRetRecordNum:" + stNextOut.nRetRecordNum);
                if (stNextOut.nRetRecordNum == 0) {
                    return pstRecordEx;
                }
                // 获取卡信息
                ToolKits.GetPointerDataToStructArr(stNextOut.pRecordList, pstRecord);

                // 获取有用的信息
                pstRecordEx = new NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC[stNextOut.nRetRecordNum];
                for (int i = 0; i < stNextOut.nRetRecordNum; i++) {
                    pstRecordEx[i] = new NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARDREC();
                    pstRecordEx[i] = pstRecord[i];
                }
            }
            LoginModule.netsdk.CLIENT_FindRecordClose(stOut.lFindeHandle);
        }
        return pstRecordEx;
    }

}
