package com.yunmai.utils;

import cn.hutool.core.util.ObjectUtil;
import com.yunmai.exception.ServiceException;
import com.sun.jna.Memory;
import com.yunmai.domain.CardDto;
import com.yunmai.common.Res;

/**
 * 参数校验工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/19
 * Copyright © wlinker.cn
 */

public class CheckUtils {

    /**
     * 校验卡信息
     * @param cardDto 卡信息
     * @param memory 图片信息
     */ 
    public static void checkCardDtoAndMemory(CardDto cardDto, Memory memory) {
//        if (memory == null) {
//            throw new ServiceException(Res.string().getSelectPicture());
//        }
        if (ObjectUtil.isEmpty(cardDto.getCardNo())) {
            throw new ServiceException(Res.string().getInputCardNo());
        }
        if (ObjectUtil.isEmpty(cardDto.getUserId())) {
            throw new ServiceException(Res.string().getInputUserId());
        }
        if (ObjectUtil.isEmpty(cardDto.getCardName())) {
            throw new ServiceException("Please input cardName");
        }
//        if (ObjectUtil.isEmpty(cardDto.getCardPasswd())) {
//            throw new ServiceException("Please input cardPasswd");
//        }

        try {
            if (cardDto.getCardNo().length() > 31) {
                throw new ServiceException(Res.string().getCardNoExceedLength() + "(31)");
            }
            if (cardDto.getUserId().length() > 31) {
                throw new ServiceException(Res.string().getUserIdExceedLength() + "(31)");
            }
            if (cardDto.getCardName().length() > 63) {
                throw new ServiceException(Res.string().getCardNameExceedLength() + "(63)");
            }
//            if (cardDto.getCardPasswd().length() > 63) {
//                throw new ServiceException(Res.string().getCardPasswdExceedLength() + "(63)");
//            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }
}
