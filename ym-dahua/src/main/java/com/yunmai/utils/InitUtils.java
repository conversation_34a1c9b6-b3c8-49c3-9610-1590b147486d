package com.yunmai.utils;


import com.yunmai.callback.DisConnectCallBack;
import com.yunmai.callback.HaveReConnectCallBack;
import com.yunmai.common.core.redis.RedisCache;
import com.yunmai.constants.DaHuaConstants;
import com.yunmai.domain.CardTeachStudInfo;
import com.yunmai.domain.Device;
import com.yunmai.domain.DeviceDto;
import com.yunmai.mapper.DaHuaDevStatusMapper;
import com.yunmai.mapper.DaHuaPersonMapper;
import com.yunmai.module.ApiModule;
import com.yunmai.module.LoginModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 初始化
 */
@Component
@Slf4j
public class InitUtils {
    /**
     * 设备断线通知回调
     */
    @Autowired
    private DisConnectCallBack disConnectCallBack;

    /**
     * 网络连接恢复
     */
    private HaveReConnectCallBack haveReConnectCallBack;

    @Autowired
    private DaHuaDevStatusMapper daHuaDevStatusMapper;

    @Autowired
    private DaHuaPersonMapper daHuaPersonMapper;

    @Autowired
    private ApiModule apiModule;

    @Autowired
    private RedisCache redisCache;

    @PostConstruct
    public void init() throws UnknownHostException {

        LoginModule.init(disConnectCallBack, haveReConnectCallBack);
        // 开启监听服务
        apiModule.startServer(InetAddress.getLocalHost().getHostAddress());

//        //初始化学生加载进入缓存
//        List<CardTeachStudInfo> cardTeachStudInfos = daHuaPersonMapper.selectAllPerson();
//        for (CardTeachStudInfo cardTeachStudInfo : cardTeachStudInfos) {
//            String personCacheKey = DaHuaConstants.PERSON_KEY + cardTeachStudInfo.getCode();
//            redisCache.setCacheObject(personCacheKey, cardTeachStudInfo, 120, TimeUnit.DAYS);
//        }
//        //初始化设备加载进入缓存
//        List<Device> deviceList = daHuaDevStatusMapper.selectAllByDevModelDeviceList();
//        for (Device device : deviceList) {
//            String deviceCacheKey = DaHuaConstants.DEVICE_KEY + device.getMachineId();
//            redisCache.setCacheObject(deviceCacheKey, device, 1, TimeUnit.DAYS);
//        }

    }

}
