package com.yunmai.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 静态文件访问映射
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/7
 * Copyright © goodits
 */
@Configuration
public class StaticFileConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        //访问 /img/**会被解析为/static/img/**
        registry.addResourceHandler("/img/**").addResourceLocations("classpath:/static/img/");
    }
}
