package com.yunmai.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
// 注册为组件
@EnableConfigurationProperties
// 启用配置自动注入功能
@ConfigurationProperties(prefix = "project")
//指定类对应的配置项前缀
@ApiModel(description = "系统描述")
public class SysInfoProperties {
    @ApiModelProperty(value = "应用中文名")
    private String chineseName;
    @ApiModelProperty(value = "应用描述")
    private String description;
    @ApiModelProperty(value = "版本")
    private String version;
    @ApiModelProperty(value = "开发")
    private String pic;

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }
}