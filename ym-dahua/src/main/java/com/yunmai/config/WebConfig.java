package com.yunmai.config;

import com.yunmai.converter.XMixedReplaceHttpMessageConverter;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 这里你可以添加一个支持 'multipart/x-mixed-replace' 类型的消息转换器
        converters.add(new XMixedReplaceHttpMessageConverter()); // 你可以实现自己的转换器
    }
}
