package com.yunmai.task;

import cn.hutool.core.util.ObjectUtil;
import com.sun.jna.Memory;
import com.yunmai.common.config.RuoYiConfig;
import com.yunmai.common.core.domain.AjaxResult;
import com.yunmai.common.utils.StringUtils;
import com.yunmai.domain.CardDto;
import com.yunmai.domain.dto.PersonDownDto;
import com.yunmai.lib.ToolKits;
import com.yunmai.mapper.DaHuaDownPersonMapper;
import com.yunmai.module.LoginModule;
import com.yunmai.service.CardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yunmai.callback.ServiceCBCallBack.DEVICE_DTO_MAP;
import static com.yunmai.common.core.domain.AjaxResult.DATA_TAG;
import static com.yunmai.common.core.domain.AjaxResult.MSG_TAG;

@Component("DaHuaDownTask")
public class DaHuaDownTask {

    private final static Logger logger = LoggerFactory.getLogger(DaHuaDownTask.class);

    @Autowired
    private DaHuaDownPersonMapper daHuaDownPersonMapper;

    @Qualifier("threadPoolTaskExecutor")
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private CardService cardService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String LOCK_KEY = "DahuaDownTaskLock";
    private static final long LOCK_EXPIRE_TIME = 1; // 锁过期时间（时）

    /**
     * 下载人员信息
     */
    public void DaHuaDown() {
        // 尝试加锁
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(LOCK_KEY, "LOCKED", LOCK_EXPIRE_TIME, TimeUnit.HOURS);

        if (Boolean.FALSE.equals(isLocked)) {
            // 如果锁已存在，跳过任务
            logger.warn("上一次任务尚未完成，本次任务跳过");
            return;
        }
        try {
            List<Integer> list = new ArrayList<>();
            list.add(0);
//        list.add(1);
            list.add(2);
            list.add(3);
            list.add(4);
            list.add(5);
            list.add(6);
            List<PersonDownDto> personDownDtoList = daHuaDownPersonMapper.personDownResultMap(list);
            logger.info("=========正在下载新增人员信息=========");
            List<PersonDownDto> downDtoList = personDownDtoList.stream()
                    .filter(i -> i.getDownStatus() == 0)
                    .collect(Collectors.toList());
            Map<String, List<PersonDownDto>> addMap = downDtoList.stream().collect(Collectors.groupingBy(PersonDownDto::getDeviceNo));
            // 提交所有异步任务，并收集 CompletableFuture
            List<CompletableFuture<Void>> futuresAdd = new ArrayList<>();
            addMap.forEach((k, v) -> {
                //设备不在线跳过
                if (ObjectUtil.isEmpty(LoginModule.loginAccessMap.get(k))) {
                    return;
                }
                // 将数据分组，每组 50 个
                List<List<PersonDownDto>> groupedData = partitionList(v, 60);
                // 遍历每组数据，提交任务到线程池
                for (List<PersonDownDto> group : groupedData) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            logger.info("开始下载人员信息，共计 {} 人...", group.size());
                            // 下载操作
                            for (PersonDownDto person : group) {
                                Memory memory = null;
                                if (!StringUtils.isBlank(person.getCardDto().getFacePath())) {
                                    memory = ToolKits.readPictureFile(RuoYiConfig.getProfile() + person.getCardDto().getFacePath());
                                }
                                // 替换为实际下载逻辑
                                AjaxResult result = cardService.add(person.getDeviceNo(), person.getDownIndex(), person.getCardDto(), memory);

                                String msg = String.valueOf(result.get(DATA_TAG));
                                if (result.isWarn()) {
                                    person.setDownStatus(1);
                                    person.setFaceDownStatus(2);
                                    person.setDownMsg(msg);
                                } else if (result.isSuccess()) {
                                    person.setFaceDownStatus(1);
                                    person.setDownStatus(1);
                                    person.setDownMsg(msg);
                                } else if (result.isError()) {
                                    person.setDownStatus(2);
                                    person.setFaceDownStatus(2);
                                }
                                if (StringUtils.isBlank(person.getCardDto().getFacePath())) {
                                    person.setFaceDownStatus(-1);
                                }
                                // 更新当前记录状态
                                daHuaDownPersonMapper.updateDownStatus(person);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            logger.error("人员信息下载失败: {}", e.getMessage());
                        }
                    }, taskExecutor);
                    futuresAdd.add(future);
                }
            });
            // 等待所有异步任务完成
            CompletableFuture.allOf(futuresAdd.toArray(new CompletableFuture[0])).join();
            logger.info("=========新增人员信息已下载完成=========");

            logger.info("=========正在重新下载人员信息=========");

            List<PersonDownDto> editDtoList = personDownDtoList.stream()
                    .filter(i -> i.getDownStatus() == 5 || (i.getDownStatus() == 1 && i.getFaceDownStatus() == 2)
                            || (i.getDownStatus() == 1 && i.getFaceDownStatus() == 5) || (i.getDownStatus() == 1 && i.getFaceDownStatus() == 6))
                    .collect(Collectors.toList());

            Map<String, List<PersonDownDto>> listMap = editDtoList.stream().collect(Collectors.groupingBy(PersonDownDto::getDeviceNo));
            // 提交所有异步任务，并收集 CompletableFuture
            List<CompletableFuture<Void>> futuresEdit = new ArrayList<>();

            listMap.forEach((k,v)->{
                //设备不在线跳过
                if (ObjectUtil.isEmpty(LoginModule.loginAccessMap.get(k))) {
                    return;
                }
                // 将数据分组，每组 60 个
                List<List<PersonDownDto>> groupedEditData = partitionList(v, 60);
                for (List<PersonDownDto> groupedEditDatum : groupedEditData) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            logger.info("开始重新下载人员信息...");
                            // 下载操作
                            for (PersonDownDto person : groupedEditDatum) {
                                CardDto cardDto = person.getCardDto();
                                if (person.getDownMsg() != null) {
                                    cardDto.setRecordNo(Integer.parseInt(person.getDownMsg()));
                                }
                                Memory memory = null;
                                if (!StringUtils.isBlank(person.getCardDto().getFacePath())) {
                                    memory = ToolKits.readPictureFile(RuoYiConfig.getProfile() + person.getCardDto().getFacePath());
                                }
                                // 替换为实际下载逻辑
                                AjaxResult result = cardService.update(person.getDeviceNo(), person.getDownIndex(), cardDto, memory);

                                String msg = (String) result.get(DATA_TAG);
                                if (result.isWarn()) {
                                    person.setDownStatus(1);
                                    person.setFaceDownStatus(6);
                                    person.setDownMsg(msg);
                                } else if (result.isSuccess()) {
                                    person.setFaceDownStatus(1);
                                    person.setDownStatus(1);
                                    person.setDownMsg(msg);
                                } else if (result.isError()) {
                                    person.setDownStatus(6);
                                    person.setFaceDownStatus(6);
                                }
                                if (StringUtils.isBlank(person.getCardDto().getFacePath())) {
                                    person.setFaceDownStatus(-1);
                                }
                                //更新当前记录状态
                                daHuaDownPersonMapper.updateDownStatus(person);
                            }
                            logger.info("人员信息修改重新完成.");
                        } catch (Exception e) {
                            e.printStackTrace();
                            logger.error("设备修改重新下载失败: {}", e.getMessage(), e);
                        }
                    }, taskExecutor);
                    futuresEdit.add(future);
                }
            });
            // 等待所有异步任务完成
            CompletableFuture.allOf(futuresEdit.toArray(new CompletableFuture[0])).join();
            logger.info("=========重新下载人员信息已下载完成=========");

            logger.info("=========正在删除人员信息=========");
            List<CompletableFuture<Void>> futuresDel = new ArrayList<>();

            List<PersonDownDto> delDtoList = personDownDtoList.stream()
                    .filter(i -> i.getDownStatus() == 3)
                    .collect(Collectors.toList());
            Map<String, List<PersonDownDto>> delMap = delDtoList.stream().collect(Collectors.groupingBy(PersonDownDto::getDeviceNo));
            delMap.forEach((k, v) -> {
                //设备不在线跳过
                if (ObjectUtil.isEmpty(LoginModule.loginAccessMap.get(k))) {
                    return;
                }
                // 将数据分组，每组 60 个
                List<List<PersonDownDto>> groupedDelData = partitionList(v, 60);
                for (List<PersonDownDto> groupedDelDatum : groupedDelData) {
                    // 异步处理任务
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            logger.info("开始删除人员信息...");
                            // 下载操作
                            for (PersonDownDto person : groupedDelDatum) {
                                // 替换为实际下载逻辑
                                AjaxResult result = cardService.delete(person.getDeviceNo(), person.getDownMsg(), person.getCardDto().getUserId());
                                String msg = (String) result.get(DATA_TAG);
                                if (result.isWarn()) {
                                    person.setDownStatus(-1);
                                    person.setFaceDownStatus(4);
                                    person.setDownMsg(msg);
                                    //更新当前记录状态
                                    daHuaDownPersonMapper.updateDownStatus(person);
                                    //                            if (status > 0) {
                                    //                                logger.info("人脸删除失败姓名：{}，卡号：{}", person.getCardDto().getCardName(), person.getCardDto().getCardNo());
                                    //                            }
                                } else if (result.isSuccess()) {
                                    //更新当前记录状态
                                    daHuaDownPersonMapper.deleteByCardNameInt(person.getUid());
                                    //                            if (status > 0) {
                                    //                                logger.info("人员删除成功姓名：{}，卡号：{}", person.getCardDto().getCardName(), person.getCardDto().getCardNo());
                                    //                            }
                                } else if (result.isError()) {
                                    person.setDownStatus(4);
                                    person.setFaceDownStatus(4);
                                    //更新当前记录状态
                                    daHuaDownPersonMapper.updateDownStatus(person);
                                    //                            if (status > 0) {
                                    //                                logger.info("人员删除失败姓名：{}，卡号：{}", person.getCardDto().getCardName(), person.getCardDto().getCardNo());
                                    //                            }
                                }
                            }
                            logger.info("人员信息下载完成.");
                        } catch (Exception e) {
                            e.printStackTrace();
                            logger.error("设备下载失败: {}", e.getMessage(), e);
                        }
                    }, taskExecutor);
                    futuresDel.add(future);
                }
            });
            // 等待所有异步任务完成
            CompletableFuture.allOf(futuresDel.toArray(new CompletableFuture[0])).join();
            logger.info("=========人员删除信息已完成=========");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 释放锁
            redisTemplate.delete(LOCK_KEY);
            logger.info("释放锁");
        }

    }


    /**
     * 将列表分组，每组指定大小
     *
     * @param list 需要分组的列表
     * @param size 每组的大小
     * @return 分组后的列表
     */
    private List<List<PersonDownDto>> partitionList(List<PersonDownDto> list, int size) {
        List<List<PersonDownDto>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return partitions;
    }
}
