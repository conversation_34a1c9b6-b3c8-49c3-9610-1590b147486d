package com.yunmai.module;


import com.yunmai.callback.ServiceCBCallBack;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.lib.ToolKits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ApiModule {
    public static NetSDKLib netsdk = NetSDKLib.NETSDK_INSTANCE;

    // 设备信息
    public static NetSDKLib.NET_DEVICEINFO_Ex m_stDeviceInfo = new NetSDKLib.NET_DEVICEINFO_Ex();

    @Autowired
    private ServiceCBCallBack serviceCBCallBack;
    //开启监听
    public NetSDKLib.LLong startServer(String ip) {
        NetSDKLib.LLong mServerHandler = netsdk.CLIENT_ListenServer(ip, 2025, 1000, serviceCBCallBack, null);
        if (0 == mServerHandler.longValue()) {
            System.err.println("Failed to start server." + ToolKits.getErrorCodePrint());
        } else {
            System.out.printf("Start server, [Server address %s][Server port %d]\n", ip, 2025);
        }
        return mServerHandler;
    }

}

