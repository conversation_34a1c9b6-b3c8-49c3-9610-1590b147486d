package com.yunmai.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yunmai.common.core.domain.AjaxResult;
import com.yunmai.common.utils.StringUtils;
import com.yunmai.domain.dto.PersonDownDto;
import com.yunmai.exception.ServiceException;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.lib.ToolKits;
import com.yunmai.module.GateModule;
import com.sun.jna.Memory;
import com.yunmai.domain.CardDto;
import com.yunmai.domain.Result;
import com.yunmai.common.Res;
import com.yunmai.service.CardService;
import com.yunmai.utils.CheckUtils;
import com.yunmai.utils.ResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.LinkedList;
import java.util.List;

/**
 * 卡操作功能实现
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @date 2021/8/6
 * Copyright © goodits
 */
@Service
public class CardServiceImpl implements CardService {

    private final static Logger logger = LoggerFactory.getLogger(CardServiceImpl.class);

    @Autowired
    private GateModule gateModule;

    @Override
    public List<CardDto> getList(String deviceNo, String cardNo) {
        List<CardDto> cardDtoList = new LinkedList<>();
        // 卡号：  为空，查询所有的卡信息
        // 获取查询句柄
        if (!StrUtil.isEmpty(cardNo)) {
            if (!GateModule.findCard(cardNo,deviceNo)) {
                return cardDtoList;
            }
        }
        try {
            int count = 0;
            int index;
            int nFindCount = 10;

            // 查询具体信息
            while (true) {
                NetSDKLib.NET_RECORDSET_ACCESS_CTL_CARD[] pstRecord = GateModule.findNextCard(nFindCount);
                if (pstRecord == null) {
                    break;
                }

                for (int i = 0; i < pstRecord.length; i++) {
                    index = i + count * nFindCount;

                    try {
                        CardDto cardDto = new CardDto();

                        // 序号
                        cardDto.setSerialNumber(String.valueOf(index + 1));
                        // 卡号
                        cardDto.setCardNo(new String(pstRecord[i].szCardNo).trim());
                        // 卡名
                        cardDto.setCardName(new String(pstRecord[i].szCardName, "GBK").trim());
                        // 用户ID
                        cardDto.setUserId(new String(pstRecord[i].szUserID).trim());
                        // 卡密码
                        cardDto.setCardPasswd(new String(pstRecord[i].szPsw).trim());
                        // 卡状态
                        cardDto.setCardStatus(pstRecord[i].emStatus);
                        cardDto.setCardStatusName(Res.string().getCardStatus(pstRecord[i].emStatus));
                        // 卡类型
                        cardDto.setCardType(pstRecord[i].emType);
                        cardDto.setCardTypeName(Res.string().getCardType(pstRecord[i].emType));
                        // 使用次数
                        cardDto.setUseTimes(String.valueOf(pstRecord[i].nUserTime));
                        // 是否首卡
                        cardDto.setFirstEnter(pstRecord[i].bFirstEnter);
                        // 是否有效
                        cardDto.setEnable(pstRecord[i].bIsValid);
                        // 有效开始时间
                        cardDto.setStartTime(pstRecord[i].stuValidStartTime.toStringTimeEx());
                        // 有效结束时间
                        cardDto.setEndTime(pstRecord[i].stuValidEndTime.toStringTimeEx());

                        cardDto.setRecordNo(pstRecord[i].nRecNo);
                        cardDtoList.add(cardDto);
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                }

                if (pstRecord.length < nFindCount) {
                    break;
                } else {
                    count++;
                }

            }
        } finally {
            // 关闭查询接口
            GateModule.findCardClose();
        }
        return cardDtoList;
    }

    @Override
    public AjaxResult add(String deviceNo, int timeIndex,CardDto cardDto, Memory memory) {
        CheckUtils.checkCardDtoAndMemory(cardDto, memory);
        // 先添加卡，卡添加成功后，再添加图片
        int useTimes;
        if (StrUtil.isEmpty(cardDto.getUseTimes()) ||  "".equals(cardDto.getUseTimes())) {
            useTimes = 0;
        } else {
            useTimes = Integer.parseInt(cardDto.getUseTimes());
        }
        int ajaxResult = gateModule.insertCard(cardDto.getCardNo(), cardDto.getUserId(), cardDto.getCardName(),
                cardDto.getCardPasswd(), cardDto.getCardStatus(),
                cardDto.getCardType(), useTimes,
                cardDto.getFirstEnter(), cardDto.getEnable(), cardDto.getStartTime(), cardDto.getEndTime(), deviceNo,timeIndex);
        if (ajaxResult < 0) {
            return AjaxResult.error("卡号下载失败");
        }
        if (!StringUtils.isBlank(cardDto.getFacePath())) {
            boolean bFaceFalgs = GateModule.addFaceInfo(cardDto.getUserId(), memory,deviceNo);

            if (!bFaceFalgs) {
                return AjaxResult.warn("人脸下载失败",ajaxResult);
            }
        }
        return AjaxResult.success(ajaxResult);
//        // 添加卡信息和人脸成功
//        if (ajaxResult > 0 && bFaceFalgs) {
//            return AjaxResult.success(ajaxResult);
//        }
//        // 添加卡信息和人脸失败
//        if (!ajaxResult.isSuccess() && !bFaceFalgs) {
//            logger.info("添加卡信息和人脸失败{}",cardDto.getCardNo());
////            throw new ServiceException(Res.string().getFailedAddCard() + " : " + cardError);
//        }
//        // 添加卡信息成功，添加人脸失败
//        if (ajaxResult.isSuccess()) {
//            throw new ServiceException(Res.string().getSucceedAddCardButFailedAddPerson() + " : " + faceError);
//        }
//        // 卡信息已存在，添加人脸成功
//        throw new ServiceException(Res.string().getCardExistedSucceedAddPerson());
    }



    @Override
    public AjaxResult update(String deviceNo, int timeIndex,CardDto cardDto, Memory memory) {
        CheckUtils.checkCardDtoAndMemory(cardDto, memory);
        // 先修改卡，卡修改成功后，再修改图片
        int useTimes;
        if (StrUtil.isEmpty(cardDto.getUseTimes())) {
            useTimes = 0;
        } else {
            useTimes = Integer.parseInt(cardDto.getUseTimes());
        }

        boolean bCardFlags = GateModule.modifyCard(cardDto.getRecordNo(), cardDto.getCardNo(),
                cardDto.getUserId(), cardDto.getCardName(),
                cardDto.getCardPasswd(), cardDto.getCardStatus(),
                cardDto.getCardType(), useTimes,
                cardDto.getFirstEnter(), cardDto.getEnable(), cardDto.getStartTime(), cardDto.getEndTime(), deviceNo, timeIndex);
        if (Boolean.FALSE.equals(bCardFlags)) {
            return AjaxResult.error("卡号修改失败");
        }
        if (memory != null) {
            boolean bFaceFalgs = GateModule.modifyFaceInfo(cardDto.getUserId(), memory,deviceNo);
            if (Boolean.FALSE.equals(bFaceFalgs)) {
                return AjaxResult.warn("人脸修改失败");
            }
        }
        return AjaxResult.success();
    }

    @Override
    public Result updateFace(String deviceNo,String userId, Memory memory) {
        boolean bFaceFalgs = GateModule.modifyFaceInfo(userId, memory, deviceNo);
        if (Boolean.FALSE.equals(bFaceFalgs)) {
            throw new ServiceException("修改人脸失败");
        }
        return ResultUtil.success("更新人脸成功");
    }


    @Override
    public AjaxResult delete(String deviceNo,String recordNo, String userId) {
        boolean deleteFaceInfo = GateModule.deleteFaceInfo(userId,deviceNo);
        boolean deleteCard = GateModule.deleteCard(Integer.parseInt(recordNo),deviceNo);
        // 删除人脸和卡信息
        if (!deleteFaceInfo || !deleteCard) {
            return AjaxResult.error(Res.string().getSucceed());
        }
        return AjaxResult.success();
    }
}
