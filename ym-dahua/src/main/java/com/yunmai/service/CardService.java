package com.yunmai.service;


import com.yunmai.common.core.domain.AjaxResult;
import com.yunmai.domain.CardDto;
import com.yunmai.domain.Result;
import com.sun.jna.Memory;

import java.util.List;

/**
 * 卡操作服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/6
 * Copyright © goodits
 */
public interface CardService {

    List<CardDto> getList(String deviceNo, String cardNo);

    AjaxResult add(String deviceNo, int timeIndex, CardDto cardDto, Memory memory);

    AjaxResult update(String deviceNo, int timeIndex,CardDto cardDto, Memory memory);

    Result updateFace(String deviceNo,String userId, Memory memory);

    AjaxResult delete(String deviceNo, String recordNo, String userId);
}
