package com.yunmai.lib.enumeration;

/**
 * 日志查询类型
 *
 * <AUTHOR>
 */
public enum DH_LOG_QUERY_TYPE {
    /**
     * 所有日志
     */
    DHLOG_ALL,
    /**
     * 系统日志
     */
    DHLOG_SYSTEM,
    /**
     * 配置日志
     */
    DHLOG_CONFIG,
    /**
     * 存储相关
     */
    DHLOG_STORAGE,
    /**
     * 报警日志
     */
    DHLOG_ALARM,
    /**
     * 录象相关
     */
    DHLOG_RECORD,
    /**
     * 帐号相关
     */
    DHLOG_ACCOUNT,
    /**
     * 清除日志
     */
    DHLOG_CLEAR,
    /**
     * 回放相关
     */
    DHLOG_PLAYBACK,
    /**
     * 前端管理运行相关
     */
    DHLOG_MANAGER

}
