package com.yunmai.lib.enumeration;

/**
 * className：EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE
 * description：
 *   枚举值,下发设置&获取设置
 *
 * author：251589
 * createTime：2020/12/29 14:04
 *
 * @version v1.0
 */
public class EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE {

    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_UNKNOWN = 0;        // 未知
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_FACE_DETECTION = 1;    // 人脸检测
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_FACE_ANALYSIS = 2;    // 人脸分析
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_FACE_ATTRIBUTE = 3;    // 人脸属性
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_FACE_COMPARE = 4;    // 人脸比对
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_NORMAL = 5;            // 智能通用行为分析
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_OBJECT_DETECT = 6;    // 智能 视频结构化
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_SMART_MOTION = 7;    // 动检,对应的结构体 NET_CFG_SMART_MOTION_DETECT
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_ENCODE_ENHANCE = 8;    // 编码增强
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_ONE_KEY_EXPAND = 9;    // 一键扩展
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_NUMBER_STAT = 10;    // 人数统计
    public static final int EM_DEV_VARIABLE_CARS_MANAGER_FUNC_TYPE_INTELLIGENT = 11;        // 智能功能,表示所有智能功能
}
