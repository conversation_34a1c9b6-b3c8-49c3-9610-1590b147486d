package com.yunmai.lib.enumeration;

public class EM_STORAGE_DISK_POWERMODE {
	public static final int 		EM_STORAGE_DISK_POWERMODE_UNKNOWN 			= 0;    // UNKnown状态（不是以下状态中的值）
	public static final int 	    EM_STORAGE_DISK_POWERMODE_NONE     			= 1;    // 未知状态
	public static final int 	    EM_STORAGE_DISK_POWERMODE_ACTIVE         	= 2;    // 活动状态
	public static final int 	    EM_STORAGE_DISK_POWERMODE_STANDBY    		= 3;    // 休眠状态
	public static final int 	    EM_STORAGE_DISK_POWERMODE_IDLE           	= 4;    // 空闲状态                                     
}
