package com.yunmai.lib.structure;

import com.yunmai.lib.NetSDKLib;

/**
 * 获取锁定状态入参 {@link NetSDKLib#CLIENT_OperateCourseCompositeChannel}
 *
 * <AUTHOR> 47040
 * @since ： Created in 2020/9/28 19:58
 */
public class NET_IN_COURSECOMPOSITE_GET_LOCKINFO extends NetSDKLib.SdkStructure {
    /**
     * 结构体大小
     */
    public int dwSize;
    /**
     * 教室ID号
     */
    public int nClassRoomID;
    /**
     * 逻辑通道号
     */
    public int nLogicChannel;

    public NET_IN_COURSECOMPOSITE_GET_LOCKINFO() {
        dwSize = this.size();
    }
}
