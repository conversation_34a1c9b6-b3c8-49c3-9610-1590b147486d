package com.yunmai.callback;

import com.sun.jna.Pointer;
import com.yunmai.config.DaHuaConfig;
import com.yunmai.domain.DeviceDto;
import com.yunmai.event.ServerOnlineEvent;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.module.LoginModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
/**
 * 侦听服务器回调函数
 */
@Component
public class ServiceCBCallBack implements NetSDKLib.fServiceCallBack {

    private static final Logger logger = LoggerFactory.getLogger(ServiceCBCallBack.class);

    public static final Map<String, DeviceDto> DEVICE_DTO_MAP = new ConcurrentHashMap<>();

    private static final ExecutorService EXECUTOR_SERVICE = Executors.newCachedThreadPool();

    private ServiceCBCallBack() {
        logger.info("订阅回调函数初始化");
    }

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public int invoke(NetSDKLib.LLong lHandle, String pIp, int wPort,
                      int lCommand, Pointer pParam, int dwParamLen, Pointer dwUserData) {

        // 将 pParam 转化为序列号
        String deviceId = extractDeviceId(pParam, dwParamLen);
        if (deviceId == null) {
            logger.error("Failed to extract device ID from callback. IP: {}, Port: {}", pIp, wPort);
            return -1;
        }

        String key = pIp + wPort;

        switch (lCommand) {
            case NetSDKLib.EM_LISTEN_TYPE.NET_DVR_DISCONNECT:
                handleDisconnect(deviceId, key);
                break;

            case NetSDKLib.EM_LISTEN_TYPE.NET_DVR_SERIAL_RETURN:
                handleDeviceRegistration(deviceId, pIp, wPort, key);
                break;

            default:
                logger.error("Unhandled command [{}] for device [{}] at IP [{}] Port [{}]",
                        lCommand, deviceId, pIp, wPort);
                break;
        }

        return 0;
    }

    /**
     * 提取设备序列号
     */
    private String extractDeviceId(Pointer pParam, int dwParamLen) {
        byte[] buffer = new byte[dwParamLen];
        pParam.read(0, buffer, 0, dwParamLen);
        try {
            return new String(buffer, "GBK").trim();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 处理设备断线回调
     */
    private void handleDisconnect(String deviceId, String key) {
        logger.info("Device [{}] disconnected. Removing from map.%n", deviceId);
        DEVICE_DTO_MAP.remove(key);
    }

    /**
     * 处理设备注册回调
     */
    private void handleDeviceRegistration(String deviceId, String pIp, int wPort, String key) {
        // 将设备信息保存到 Map
        DeviceDto deviceDto = new DeviceDto(deviceId, pIp, wPort, "admin", DaHuaConfig.getPassword());
        DEVICE_DTO_MAP.put(key, deviceDto);

        logger.info("Device registered: [Address: {}] [Port: {}] [ID: {}]", pIp, wPort, deviceId);

        // 执行设备登录
        login(key);

        // 异步执行后续逻辑
//        EXECUTOR_SERVICE.submit(() -> {
            try {
                Thread.sleep(500); // 延迟，确保资源加载完成
                sendOnlineEvent(deviceDto);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                System.err.printf("Initialization for device [%s] was interrupted.%n", deviceId);
            }
//        }
//        );
    }

    /**
     * 登录设备
     */
    public void login(String deviceSn) {
        DeviceDto device = DEVICE_DTO_MAP.get(deviceSn);
        LoginModule.login(device.getM_strIp(),
                device.getM_nPort(),
                device.getM_strUser(),
                device.getM_strPassword(),
                device.getId());
    }

    /**
     * 发送设备在线事件
     */
    private void sendOnlineEvent(DeviceDto deviceDto) {
        ServerOnlineEvent event = new ServerOnlineEvent(this);
        event.setMediaServerItemList(deviceDto);
        applicationEventPublisher.publishEvent(event);
//        System.out.printf("Online event published for device [%s].%n", deviceDto.getId());
    }
}
