package com.yunmai.callback;

import com.sun.jna.Pointer;
import com.yunmai.common.Res;
import com.yunmai.domain.DeviceDto;
import com.yunmai.event.ServerOfflineEvent;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.module.LoginModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class DisConnectCallBack implements NetSDKLib.fDisConnect {

    private static final Logger logger = LoggerFactory.getLogger(DisConnectCallBack.class);

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;


    @Override
    public void invoke(NetSDKLib.LLong lLoginID, String pchDVRIP, int nDVRPort, Pointer dwUser) {
        logger.info("Device[{}}] Port[{}}] DisConnect!\n", pchDVRIP, nDVRPort);
        //移除map中的设备
        DeviceDto deviceDto = ServiceCBCallBack.DEVICE_DTO_MAP.get(pchDVRIP + nDVRPort);
        if (deviceDto != null) {
            removeDevice(deviceDto.getId());
        }
        // 断线提示
        logger.info(Res.string().getFaceRecognition() + " : " + Res.string().getDisConnectReconnecting());
    }


    public void removeDevice(String deviceId) {
        if (LoginModule.loginAccessMap.containsKey(deviceId)) {
            logger.info("移除设备：" + deviceId);
            ServerOfflineEvent event = new ServerOfflineEvent(this);
            event.setMediaServerId(deviceId);
            applicationEventPublisher.publishEvent(event);
            LoginModule.loginAccessMap.remove(deviceId);
        } else {
            logger.info("设备不存在：" + deviceId);
        }
        logger.info("当前设备列表：" + LoginModule.loginAccessMap.keySet());
    }

}
