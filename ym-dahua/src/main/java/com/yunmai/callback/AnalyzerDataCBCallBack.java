package com.yunmai.callback;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import com.yunmai.common.config.RuoYiConfig;
import com.yunmai.common.core.redis.RedisCache;
import com.yunmai.constants.DaHuaConstants;
import com.yunmai.domain.CardTeachStudInfo;
import com.yunmai.domain.Device;
import com.yunmai.domain.FaceRecord;
import com.yunmai.domain.SignRecord;
import com.yunmai.domain.dto.RecordEventDataDto;
import com.yunmai.domain.dto.RecordEventDto;
import com.yunmai.event.ServerStatusEventListener;
import com.yunmai.lib.NetSDKLib;
import com.yunmai.lib.ToolKits;
import com.yunmai.mapper.*;
import com.yunmai.module.LoginModule;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Component
public class AnalyzerDataCBCallBack implements NetSDKLib.fAnalyzerDataCallBack {

    private static final Logger logger = LoggerFactory.getLogger(AnalyzerDataCBCallBack.class);

    @Autowired
    private RedisCache redisCache;


    @Autowired
    private DaHuaDevStatusMapper daHuaDevStatusMapper;

    @Autowired
    private DaHuaPersonMapper daHuaPersonMapper;

    @Autowired
    private DaHuaDevRecordMapper daHuaDevRecordMapper; // 服务层处理数据库操作

    @Autowired
    private DaHuaDevSignRecordMapper daHuaDevSignRecordMapper;

    @Autowired
    private SignPeriodMapper signPeriodMapper;

    @Override
    public int invoke(
            NetSDKLib.LLong lAnalyzerHandle,
            int dwAlarmType,
            Pointer pAlarmInfo,
            Pointer pBuffer,
            int dwBufSize,
            Pointer dwUser,
            int nSequence,
            Pointer reserved) {

        if (lAnalyzerHandle.longValue() == 0 || pAlarmInfo == null) {
            return -1;
        }
        // 提取设备号
        String deviceId = ServerStatusEventListener.deviceHandleMap.get(lAnalyzerHandle);
        if (deviceId == null) {
            logger.error("无法提取设备ID，忽略事件");
            return -1;
        }

        logger.info("接收到设备号: {}", deviceId);

        // 解析事件信息
        NetSDKLib.DEV_EVENT_ACCESS_CTL_INFO msg = new NetSDKLib.DEV_EVENT_ACCESS_CTL_INFO();
        ToolKits.GetPointerData(pAlarmInfo, msg);
        String cardNo = "";
        String userId = "";
//        String utcTimeString = msg.RealUTC.toString().replaceAll("\\b(\\d)\\b", "0$1");
        Date date = new Date();

        try {
            cardNo = new String(msg.szCardNo, "GBK").trim();
            logger.info("卡号：{}", cardNo);
//            name = new String(msg.szCardName, "GBK").trim();
            userId = new String(msg.szUserID, "GBK").trim();
            logger.info("用户id：{}", userId);

//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
//
//            // 解析 UTC 时间为 ZonedDateTime
//            ZonedDateTime utcTime = ZonedDateTime.parse(utcTimeString, formatter.withZone(ZoneId.of("UTC")));
//
//            // 将 UTC 时间转换为本地时间
//            ZonedDateTime localDateTime = utcTime.withZoneSameInstant(ZoneId.systemDefault()); // 转换为本地时区
//
//            // 将 ZonedDateTime 转换为 Date
//            Instant instant = localDateTime.toInstant();
//            date = Date.from(instant);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        switch (dwAlarmType) {
            case NetSDKLib.EVENT_IVS_ACCESS_CTL: // 门禁事件
                String photoPath = savePhoto(pBuffer, dwBufSize, cardNo);
                //远程开门
                if (StringUtils.isBlank(userId)) {
                    logger.info("人员id为空");
                    break;
                }
                String card = String.format("%012d", Long.parseLong(swapEndian(cardNo), 16));
                logger.info("卡号：{},用户 ：{}", card, userId);
                CardTeachStudInfo cardTeachStudInfo = redisCache.getCacheObject(DaHuaConstants.PERSON_KEY + userId);
                if (ObjectUtil.isEmpty(cardTeachStudInfo)) {
                    CardTeachStudInfo cardTeachStudCache = new CardTeachStudInfo();
                    cardTeachStudCache.setCard(card);
                    cardTeachStudCache.setCode(userId);
                    //从数据库中获取存入缓存
                    cardTeachStudInfo = daHuaPersonMapper.selectAllPersonByCondition(cardTeachStudCache);
                    if (!ObjectUtil.isEmpty(cardTeachStudInfo)) {
                        String personCacheKey = DaHuaConstants.PERSON_KEY + cardTeachStudInfo.getCode();
                        redisCache.setCacheObject(personCacheKey, cardTeachStudInfo, 120, TimeUnit.DAYS);
                    } else {
                        break;
                    }
                }

                Device device = redisCache.getCacheObject(DaHuaConstants.DEVICE_KEY + deviceId);
                if (ObjectUtil.isEmpty(device)) {
                    Device deviceCache = new Device();
                    deviceCache.setMachineId(deviceId);
                    //从数据库中获取存入缓存
                    device = daHuaDevStatusMapper.searchAllByDevSnDevice(deviceCache);
                    if (!ObjectUtil.isEmpty(device)) {
                        String personCacheKey = DaHuaConstants.DEVICE_KEY + device.getMachineId();
                        redisCache.setCacheObject(personCacheKey, device, 1, TimeUnit.DAYS);
                    } else {
                        break;
                    }
                }
                RecordEventDataDto dataDto = new RecordEventDataDto();
                // 创建对象
                RecordEventDto record = new RecordEventDto();
                String recordId = UUID.randomUUID().toString();
                record.setUsedType(22);
                record.setUid(recordId);
                record.setMachineId(deviceId); // 示例设备 ID
                record.setReadHead(0);
                record.setRecordTime(date); // 示例时间
                record.setInfoId(cardTeachStudInfo.getUid());
                record.setPersonType(2);

                dataDto.setData(record);
                String periodUidByTime = signPeriodMapper.getPeriodUidByTime(date);
                if ("29".equals(device.getUsed()) && !"2".equals(cardTeachStudInfo.getInfoType())) {
                    //存入签到记录表判断当前签到的时段
                    if (StringUtils.isBlank(periodUidByTime)) {
                        logger.info("未在签到时间段");
                        break;
                    }
                    //查询家长绑定的学生id
                    List<String> studentsList = daHuaDevSignRecordMapper.queryAllByParentInfoId(cardTeachStudInfo.getUid());
                    for (String uid : studentsList) {
                        String redisKey = DaHuaConstants.SIGN_STATUS_KEY + uid;
                        redisCache.setCacheObject(redisKey, "1", 3, TimeUnit.HOURS);
                        SignRecord signRecord = new SignRecord();
                        signRecord.setInfoId(uid);
                        signRecord.setPeriodUid(periodUidByTime);
                        signRecord.setStatus(0);
                        signRecord.setSigninTime(date);
                        daHuaDevSignRecordMapper.insertSignRecord(signRecord);
                    }
                    break;
                }

                int errorCode = msg.nErrorCode;
                //1 远程成功 2 远程开门失败
                int status = 1;
                if (errorCode == 96) {
                    //需要判断是否开门 家长是否签到
                    if ("2".equals(cardTeachStudInfo.getInfoType())) {
                        if (StringUtils.isBlank(periodUidByTime)) {
                            logger.info("未在签到时间段");
                        }
                        //从缓存中获取
                        String redisSignRecordKey = DaHuaConstants.SIGN_STATUS_KEY + cardTeachStudInfo.getUid();
                        String signStatus = null;
                        try {
                            signStatus = redisCache.getCacheObject(redisSignRecordKey);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (StringUtils.isBlank(signStatus)) {
                            int condition = daHuaDevSignRecordMapper.getSignRecordByCondition(periodUidByTime, cardTeachStudInfo.getUid());
                            if (condition > 0) {
                                //推送记录
                                openDoor(deviceId);
                                String redisKey = DaHuaConstants.ACCESS_STATUS_KEY + cardTeachStudInfo.getUid();
                                redisCache.setCacheObject(redisKey, 1, 3, TimeUnit.HOURS);
                            } else {
                                status = 2;
                            }
                        } else {
                            // 判断签到状态是否为已签到（"1"）
                            if ("1".equals(signStatus)) {
                                //推送记录
                                openDoor(deviceId);
                                String redisKey = DaHuaConstants.ACCESS_STATUS_KEY + cardTeachStudInfo.getUid();
                                redisCache.setCacheObject(redisKey, 1, 3, TimeUnit.HOURS);
                            } else {
                                // 未签到的逻辑
                                status = 2;
                            }
                        }
                    } else if ("1".equals(cardTeachStudInfo.getInfoType())) {
                        openDoor(deviceId);
                    }
                }

                if (status == 1) {
                    //推送记录
                    String jsonString = JSON.toJSONString(dataDto);
                    redisCache.publishMessage("/service/recordlist", jsonString);
                }
                //保存数据
                handleAccessEvent(date, photoPath, device.getUid(), cardTeachStudInfo.getUid(), deviceId, status, recordId);
                break;
            case NetSDKLib.EVENT_IVS_FACERECOGNITION: // 人脸识别事件
                handleFaceRecognitionEvent(pAlarmInfo, pBuffer, dwBufSize);
                break;
            default:
                break;
        }
        return 0;
    }


    private String extractDeviceIdFromEvent(Pointer dwUser) {
        if (dwUser == null) {
            logger.error("dwUser 指针为空，无法提取设备号");
            return null;
        }
        try {
            return dwUser.getString(0); // 偏移量 0 读取设备号字符串
        } catch (Exception e) {
            logger.error("提取设备ID失败", e);
            return null;
        }
    }


    private void openDoor(String deviceId) {
        NetSDKLib.NET_CTRL_ACCESS_OPEN openInfo = new NetSDKLib.NET_CTRL_ACCESS_OPEN();
        openInfo.nChannelID = 0;
        openInfo.emOpenDoorType = NetSDKLib.EM_OPEN_DOOR_TYPE.EM_OPEN_DOOR_TYPE_REMOTE;
        Pointer pointer = new Memory(openInfo.size());
        ToolKits.SetStructDataToPointer(openInfo, pointer, 0);
        boolean ret = LoginModule.netsdk.CLIENT_ControlDeviceEx(LoginModule.loginAccessMap.get(deviceId).getM_hLoginHandle(),
                NetSDKLib.CtrlType.CTRLTYPE_CTRL_ACCESS_OPEN, pointer, null, 3000);
        if (!ret) {
            logger.info("远程开门失败！");
        }
    }

    public void handleAccessEvent(Date nowDate, String photoPath, String devId, String infoId, String devSn, int status, String uid) {

//        Device device = redisCache.getCacheObject(DaHuaConstants.DEVICE_KEY + deviceId);
//        if (ObjectUtil.isEmpty(device)) {
//            Device deviceCache = new Device();
//            deviceCache.setMachineId(deviceId);
//            //从数据库中获取存入缓存
//            device = daHuaDevStatusMapper.searchAllByDevSnDevice(deviceCache);
//            if (!ObjectUtil.isEmpty(device)) {
//                String personCacheKey = DaHuaConstants.DEVICE_KEY + device.getMachineId();
//                redisCache.setCacheObject(personCacheKey, device, 1, TimeUnit.DAYS);
//            } else {
//                return;
//            }
//        }
        // 保存数据库记录
        FaceRecord record = new FaceRecord();
        record.setDevId(devId);
        record.setUid(uid);
        record.setInfoId(infoId);
        record.setMachineId(devSn);
        record.setRecordTime(nowDate);
        record.setFaceDown(1);
        record.setStatus(status);
        record.setRecordType(0);
        record.setTiWen(new BigDecimal("0.0"));
        record.setFacePath(photoPath);

        daHuaDevRecordMapper.insertFaceRecord(record);
    }

    private void handleFaceRecognitionEvent(Pointer pAlarmInfo, Pointer pBuffer, int dwBufSize) {

    }

    private String savePhoto(Pointer pBuffer, int dwBufSize, String cardNumber) {
        LocalDate today = LocalDate.now();
        // 格式化年月为单独的文件夹名称
        String yearMonthFolder = today.format(DateTimeFormatter.ofPattern("yyyyMM"));

        // 格式化日为子目录名称
        String dayFolder = String.valueOf(today.getDayOfMonth());
        //文件夹
        String yunMaiFaceFile = RuoYiConfig.getProfile();
        // 创建存储路径
        String dirPath = "/" + "recordimg" + "/" + yearMonthFolder + "/" + dayFolder;
        File dir = new File(yunMaiFaceFile + dirPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        // 照片保存路径
        String filePath = dirPath + "/" + "card_" + cardNumber + "_" + System.currentTimeMillis() + ".jpg";

        // 保存照片
        try {
            byte[] buffer = pBuffer.getByteArray(0, dwBufSize);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(buffer);
            BufferedImage image = ImageIO.read(inputStream);
            if (image != null) {
                ImageIO.write(image, "jpg", new File(yunMaiFaceFile + filePath));
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return filePath;
    }


    /**
     * 将十六进制字符串进行大小端转换
     *
     * @param hexValue 输入的十六进制字符串（长度必须为8）
     * @return 大小端转换后的十六进制字符串
     */
    public static String swapEndian(String hexValue) {
        if (hexValue.length() != 8) {
            throw new IllegalArgumentException("输入的十六进制字符串长度必须为8！");
        }

        // 分成4个字节，并倒序拼接
        String byte1 = hexValue.substring(0, 2);
        String byte2 = hexValue.substring(2, 4);
        String byte3 = hexValue.substring(4, 6);
        String byte4 = hexValue.substring(6, 8);

        return byte4 + byte3 + byte2 + byte1; // 倒序拼接
    }

    // 将16进制字符串转换为10进制整数
    public static String hexToDecimal(String hex) {
        // 使用 Integer.parseInt() 方法进行转换，第二个参数指定是16进制
        return String.valueOf(Long.parseLong(hex, 16));
    }

    // 将10进制整数转换为16进制字符串
    public static String decimalToHex(Long decimal) {
        // 使用 Integer.toHexString() 方法进行转换
        return Long.toHexString(decimal);
    }
}
