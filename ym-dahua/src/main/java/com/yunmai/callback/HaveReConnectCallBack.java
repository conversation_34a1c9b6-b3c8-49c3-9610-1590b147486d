package com.yunmai.callback;

import com.sun.jna.Pointer;
import com.yunmai.common.Res;
import com.yunmai.lib.NetSDKLib;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class HaveReConnectCallBack implements NetSDKLib.fHaveReConnect {

    private static final Logger logger = LoggerFactory.getLogger(HaveReConnectCallBack.class);

    @Override
    public void invoke(NetSDKLib.LLong lLoginID, String pchDVRIP, int nDVRPort, Pointer dwUser) {
        logger.info("ReConnect Device[{}] Port[{}]\n", pchDVRIP, nDVRPort);
        // 重连提示
        logger.info(Res.string().getFaceRecognition() + " : " + Res.string().getOnline());
    }
}
