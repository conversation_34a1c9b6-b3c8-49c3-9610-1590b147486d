package com.yunmai.mapper;

import com.yunmai.domain.FaceRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 设备状态 数据层
 */
public interface DaHuaDevRecordMapper {

    /**
     * 插入通行记录
     */
    int insertFaceRecord(FaceRecord faceRecord);

    /**
     * 查询人脸识别记录
     * @return
     */
    int queryByDevIdAndInfoIdInt(@Param("uid") String uid,
                                 @Param("machineId") String machineId,
                                 @Param("recordTime") String recordTime);


}
