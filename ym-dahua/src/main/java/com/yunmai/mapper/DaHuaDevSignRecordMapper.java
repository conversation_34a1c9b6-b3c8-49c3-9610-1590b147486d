package com.yunmai.mapper;

import com.yunmai.domain.FaceRecord;
import com.yunmai.domain.SignRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备状态 数据层
 */
public interface DaHuaDevSignRecordMapper {

    /**
     * 插入签到记录
     */
    int insertSignRecord(SignRecord signRecord);

    /**
     * @param periodUid 时段表 UID
     * @param infoId 家长 ID
     * @return 符合条件的记录总数
     */
    int getSignRecordByCondition(@Param("periodUid") String periodUid, @Param("infoId") String infoId);


    /**
     * 获取时段
     */
    String getSignPeriodUid();

    /**
     * 获取学生id
     */
    List<String> queryAllByParentInfoId(@Param("parentId") String parentId);
}
