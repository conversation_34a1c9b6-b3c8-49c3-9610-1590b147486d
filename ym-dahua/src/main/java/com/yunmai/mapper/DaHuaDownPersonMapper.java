package com.yunmai.mapper;

import com.yunmai.domain.dto.PersonDownDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备状态 数据层
 */
public interface DaHuaDownPersonMapper {

    /**
     * 获取下载人员信息
     * @return
     */
    List<PersonDownDto> personDownResultMap(@Param("statusList") List<Integer> statusList);

    /**
     * 下发状态修改
     */
    int updateDownStatus(PersonDownDto personDownDto);

    /**
     * 删除人员信息
     */
    int deleteByCardNameInt(String uid);
}
