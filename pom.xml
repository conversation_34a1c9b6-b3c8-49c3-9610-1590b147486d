<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.yunmai</groupId>
    <artifactId>yunmai</artifactId>
    <version>3.8.8</version>

    <name>yunmai</name>
    <url>http://www.ymiots.com</url>
    <description>云麦科技管理系统</description>
    
    <properties>
        <yunmai.version>3.8.8</yunmai.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <spring-framework.version>5.3.33</spring-framework.version>
        <spring-security.version>5.7.12</spring-security.version>
        <druid.version>1.2.23</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.7</pagehelper.boot.version>
        <fastjson.version>2.0.53</fastjson.version>
        <oshi.version>5.8.6</oshi.version>
        <commons.io.version>2.13.0</commons.io.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringSecurity的依赖配置-->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.15</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- Swagger3依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.yunmai</groupId>
                <artifactId>yunmai-quartz</artifactId>
                <version>${yunmai.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.yunmai</groupId>
                <artifactId>yunmai-generator</artifactId>
                <version>${yunmai.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.yunmai</groupId>
                <artifactId>yunmai-framework</artifactId>
                <version>${yunmai.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.yunmai</groupId>
                <artifactId>yunmai-system</artifactId>
                <version>${yunmai.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.yunmai</groupId>
                <artifactId>yunmai-common</artifactId>
                <version>${yunmai.version}</version>
            </dependency>

            <!--大华对接-->
<!--            <dependency>-->
<!--                <groupId>com.yunmai</groupId>-->
<!--                <artifactId>yunmai-dahua</artifactId>-->
<!--                <version>${yunmai.version}</version>-->
<!--            </dependency>-->

<!--            海康对接-->
<!--            <dependency>-->
<!--                <groupId>com.yunmai</groupId>-->
<!--                <artifactId>yunmai-haikang</artifactId>-->
<!--                <version>${yunmai.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.yunmai</groupId>-->
<!--                <artifactId>yunmai-hongruan</artifactId>-->
<!--                <version>${yunmai.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.yunmai</groupId>-->
<!--                <artifactId>yunmai-zk</artifactId>-->
<!--                <version>${yunmai.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.yunmai</groupId>
                <artifactId>yunmai-flpark</artifactId>
                <version>${yunmai.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ym-admin</module>
        <module>ym-framework</module>
        <module>ym-system</module>
        <module>ym-quartz</module>
        <module>ym-generator</module>
        <module>ym-common</module>
<!--        <module>ym-dahua</module>-->
<!--        <module>ym-haikang</module>-->
<!--        <module>ym-hongruan</module>-->
        <module>ym-flparking</module>
<!--        <module>ym-zk</module>-->
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>