package com.yunmai.ymparking.service.impl;

import com.yunmai.ymparking.domain.CardInfo;
import com.yunmai.ymparking.domain.CardTeachStudInfo;
import com.yunmai.ymparking.domain.ConsumePayRecord;
import com.yunmai.ymparking.domain.ParkingRecord;
import com.yunmai.ymparking.domain.vo.VisitorCarVO;
import com.yunmai.ymparking.mapper.VisitorPlateMapper;
import com.yunmai.ymparking.service.VisitorPlateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Service
public class VisitorPlateServiceImpl implements VisitorPlateService {

    @Autowired
    private VisitorPlateMapper visitorPlateMapper;

    @Override
    public CardInfo getAccountInfoAndCodeById(String plateNumber, String byCode) {
        return visitorPlateMapper.getAccountInfoAndCodeById(plateNumber,byCode);
    }

    @Override
    public CardInfo getAccountInfoById(String plateNumber) {
        return visitorPlateMapper.getAccountInfoById(plateNumber);
    }

    @Override
    public BigDecimal getConsumeByPlateNo(String cardId) {
        return visitorPlateMapper.getConsumeByPlateNo(cardId);
    }

    @Override
    public VisitorCarVO selectVisitorInfo(String plateNo) {
        return visitorPlateMapper.selectVisitorInfo(plateNo);
    }

    @Override
    public CardTeachStudInfo queryByPlateNoAndInfoIdInt(String plateNo,String byCode) {
        return visitorPlateMapper.queryByPlateNoAndInfoIdInt(plateNo,byCode);
    }

    @Override
    public CardTeachStudInfo queryByPlateNo(String plateNo) {
        return visitorPlateMapper.queryByPlateNo(plateNo);
    }

    @Override
    public int savePlateConsumeRecord(ConsumePayRecord consumePayRecord) {
        return visitorPlateMapper.savePlateConsumeRecord(consumePayRecord);
    }

    @Override
    public boolean checkDuplicateConsumeRecord(String cardId, String devId, BigDecimal money, String vehicleNo) {
        // 检查5分钟内是否有相同的扣费记录
        int count = visitorPlateMapper.checkDuplicateConsumeRecord(cardId, devId, money);
        return count > 0;
    }
}
