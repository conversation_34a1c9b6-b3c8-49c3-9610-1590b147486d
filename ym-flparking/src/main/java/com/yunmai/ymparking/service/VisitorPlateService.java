package com.yunmai.ymparking.service;

import com.yunmai.ymparking.domain.CardInfo;
import com.yunmai.ymparking.domain.CardTeachStudInfo;
import com.yunmai.ymparking.domain.ConsumePayRecord;
import com.yunmai.ymparking.domain.ParkingRecord;
import com.yunmai.ymparking.domain.vo.VisitorCarVO;
import io.lettuce.core.dynamic.annotation.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface VisitorPlateService {

    /**
     * 查询账户信息
     */
    CardInfo getAccountInfoAndCodeById(String plateNumber,
                                       String byCode);

    /**
     * 查询账户信息
     */
    CardInfo getAccountInfoById(String plateNumber);

    /**
     * 是否为内部人员车辆
     * @param plateNo
     * @return
     */
    CardTeachStudInfo queryByPlateNoAndInfoIdInt(String plateNo,
                                                 String byCode);

    /**
     * 是否为内部人员车辆
     * @param plateNo
     * @return
     */
    CardTeachStudInfo queryByPlateNo(String plateNo);

    /**
     * 查询未结算的金额
     */
    BigDecimal getConsumeByPlateNo(String cardId);

    /**
     * 查询访客信息
     *
     * @return 访客信息列表
     */
    VisitorCarVO selectVisitorInfo(String plateNo);


    /**
     * 扣费接口
     */
    int savePlateConsumeRecord(ConsumePayRecord consumePayRecord);
}
