package com.yunmai.ymparking.mapper;


import com.yunmai.ymparking.domain.CardInfo;
import com.yunmai.ymparking.domain.CardTeachStudInfo;
import com.yunmai.ymparking.domain.ConsumePayRecord;
import com.yunmai.ymparking.domain.vo.VisitorCarVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 设备状态 数据层
 */
public interface VisitorPlateMapper {

    /**
     * 查询账户信息
     */
    CardInfo getAccountInfoById(String plateNumber);

    /**
     * 查询账户信息
     */
    CardInfo getAccountInfoAndCodeById(@Param("plateNo") String plateNo, @Param("byCode") String byCode);
    /**
     * 查询未结算的金额
     */
    BigDecimal getConsumeByPlateNo(String cardId);

    /**
     * 查询访客信息
     *
     * @return 访客信息列表
     */
    VisitorCarVO selectVisitorInfo(String plateNo);

    /**
     * 是否为内部人员车辆
     * @param plateNo
     * @return
     */
    CardTeachStudInfo queryByPlateNoAndInfoIdInt(@Param("plateNo") String plateNo, @Param("byCode") String byCode);

    /**
     * 扣费接口
     */
    int savePlateConsumeRecord(ConsumePayRecord consumePayRecord);

    /**
     * 检查是否存在重复的扣费记录（5分钟内相同卡ID、设备ID、金额）
     * @param cardId 卡ID
     * @param devId 设备ID
     * @param money 金额
     * @return 重复记录数量
     */
    int checkDuplicateConsumeRecord(@Param("cardId") String cardId,
                                   @Param("devId") String devId,
                                   @Param("money") BigDecimal money);

    /**
     * 按车牌查询
     * @param plateNo
     * @return
     */
    CardTeachStudInfo queryByPlateNo(String plateNo);

}
