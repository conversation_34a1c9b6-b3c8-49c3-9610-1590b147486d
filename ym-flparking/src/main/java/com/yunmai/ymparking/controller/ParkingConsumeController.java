package com.yunmai.ymparking.controller;

import com.yunmai.common.annotation.RepeatSubmit;
import com.yunmai.common.core.redis.RedisCache;
import com.yunmai.common.utils.redis.RedisLockUtil;
import com.yunmai.common.constant.CacheConstants;
import com.yunmai.common.core.redis.RedisCache;
import com.yunmai.common.exception.enums.GlobalErrorCodeConstants;
import com.yunmai.common.pojo.CommonResult;
import com.yunmai.ymparking.domain.CardInfo;
import com.yunmai.ymparking.domain.CardTeachStudInfo;
import com.yunmai.ymparking.domain.ConsumePayRecord;
import com.yunmai.ymparking.domain.dto.AccountInfo;
import com.yunmai.ymparking.domain.dto.OrderDetail;
import com.yunmai.ymparking.domain.dto.PaymentResult;
import com.yunmai.ymparking.domain.vo.VisitorCarVO;
import com.yunmai.ymparking.service.VisitorPlateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeUnit;

/**
 * 停车缴费控制器
 * 负责处理与停车费用支付相关的请求和逻辑
 *
 * <AUTHOR>
 * @since 2023-07-01
 */
@Validated // 启用参数校验
@RestController
@RequestMapping("/api/parking")
@Tag(name = "停车缴费管理", description = "包含车辆进出缴费相关接口，支持车牌识别、费用计算和支付处理")
public class ParkingConsumeController {

    @Autowired
    private VisitorPlateService visitorPlateService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 处理车辆缴费请求
     */
    @PostMapping("/consume")
    @Operation(
            summary = "车辆缴费接口",
            description = "基于车牌号完成停车费用支付流程"
    )
    public CommonResult<PaymentResult> consume(
            @Parameter(
                    name = "vehicleNo",
                    in = ParameterIn.QUERY,
                    description = "车牌号码（支持常规车牌和新能源车牌）",
                    required = true,
                    schema = @Schema(
                            type = "string",
                            pattern = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                            example = "粤B12345"
                    )
            )
            @Pattern(regexp = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                    message = "车牌号格式错误：需符合[汉字][大写字母][5-6位字母/数字]格式")
            @RequestParam("vehicleNo") String vehicleNo,
            @Parameter(
                    name = "sn",
                    in = ParameterIn.QUERY,
                    description = "当前停车场编号或者设备编号(区分哪个闸机缴费)",
                    required = true
            )
            @RequestParam("sn") String sn,
            @Parameter(
                    name = "remark",
                    in = ParameterIn.QUERY,
                    description = "扣费原因"
            )
            @RequestParam(value = "remark", required = false) String remark,
            @Parameter(
                    name = "money",
                    in = ParameterIn.QUERY,
                    description = "支付金额（人民币）",
                    required = true,
                    schema = @Schema(
                            type = "number",
                            format = "Long",
                            minimum = "1",
                            maximum = "999999",
                            example = "1550"
                    )
            )
            @DecimalMin(value = "1", message = "金额不能低于最低支付额(1分)")
            @DecimalMax(value = "999999", message = "金额超过系统最大金额限制(999999分)")
            @RequestParam("money") Long money,
            @Parameter(
                    name = "secretKey",
                    in = ParameterIn.QUERY,
                    description = "秘钥",
                    required = true,
                    schema = @Schema(
                            type = "string",
                            example = "123"
                    )
            )
            @RequestParam(value = "secretKey", required = true) String secretKey) {

        if (secretKey == null || secretKey.isEmpty()) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "请求参数不正常");
        }
        if (!"eyJhbGciOiJSUzI1NiIsImtpZCI6IjNlNzk2NDZjN".equals(secretKey)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "秘钥错误");
        }
        String uid = UUID.randomUUID().toString();

        //先判断是否为内部车辆
        CardTeachStudInfo cardTeachStudInfo = visitorPlateService.queryByPlateNo(vehicleNo);

        if (cardTeachStudInfo == null) {
            //走访客车辆逻辑
            //查找被访人信息进行扣款
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "当前车牌未注册");
        }
        CardInfo accountInfoById = visitorPlateService.getAccountInfoById(vehicleNo);

        // 使用分布式锁防止重复订单
        // 基于关键业务参数生成唯一锁键：卡ID + 设备ID + 金额
        String lockKey = String.format("parking_consume:%s:%s:%s",
                accountInfoById.getCardId(), sn, money);

        // 尝试获取分布式锁，5分钟过期
        String lockValue = RedisLockUtil.tryLock(lockKey, 5, TimeUnit.MINUTES);
        if (lockValue == null) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),
                    "检测到重复扣费订单，请勿重复操作");
        }

        try {

        ConsumePayRecord consumePayRecord = new ConsumePayRecord();
        consumePayRecord.setUid(uid);
        consumePayRecord.setMoney(fenToYuan(money));
        consumePayRecord.setCardId(accountInfoById.getCardId());
        consumePayRecord.setInfoId(accountInfoById.getInfoId());
        consumePayRecord.setDevId(sn);
        consumePayRecord.setCardNo(accountInfoById.getCardNo());
        consumePayRecord.setMachineId(sn);
        consumePayRecord.setPlaceId("");
        consumePayRecord.setSchemeId("");
        consumePayRecord.setMerId("");
        consumePayRecord.setRemark("停车扣费" + remark + "车牌：" + vehicleNo);
        int savePlateConsumeRecord = visitorPlateService.savePlateConsumeRecord(consumePayRecord);

            if (savePlateConsumeRecord <= 0) {
                return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "未知错误");
            }

            LocalDateTime now = LocalDateTime.now();
            // 定义格式（"yyyy年MM月dd日 HH时mm分ss秒"）
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH时mm分ss秒");

            // 格式化输出
            String formattedDateTime = now.format(formatter);
            // 支付实现
            PaymentResult result = PaymentResult.builder()
                    .vehicleNo(vehicleNo)
                    .paidAmount(money)
                    .payTime(formattedDateTime)
                    .transactionId(uid)
                    .remark(remark)
                    .build();

            return CommonResult.success(result);

        } finally {
            // 释放分布式锁
            RedisLockUtil.releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 将分转为元（BigDecimal，保留2位小数）
     *
     * @param fen 分（Long类型）
     * @return 元（BigDecimal类型，如 100分 → 1.00元）
     */
    public static BigDecimal fenToYuan(Long fen) {
        if (fen == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(fen)
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }


    /**
     * 查询访客车辆
     */
    @Operation(
            summary = "查询访客车辆",
            description = "根据申请日期查询访客车辆信息"
    )
    @PostMapping("/visitorCars")
    public CommonResult<VisitorCarVO> getVisitorCars(
            @Parameter(
                    name = "vehicleNo",
                    in = ParameterIn.QUERY,
                    description = "车牌号码（支持常规车牌和新能源车牌）",
                    required = true,
                    schema = @Schema(
                            type = "string",
                            pattern = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                            example = "粤B12345"
                    )
            )
            @Pattern(regexp = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                    message = "车牌号格式错误：需符合[汉字][大写字母][5-6位字母/数字]格式")
            @RequestParam("vehicleNo") String vehicleNo,
            @Parameter(
                    name = "secretKey",
                    in = ParameterIn.QUERY,
                    description = "秘钥",
                    required = true,
                    schema = @Schema(
                            type = "string",
                            example = "123"
                    )
            ) String secretKey) {
        if (secretKey == null || secretKey.isEmpty()) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "请求参数不正常");
        }
        if (!"eyJhbGciOiJSUzI1NiIsImtpZCI6IjNlNzk2NDZjN".equals(secretKey)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "秘钥错误");
        }
        VisitorCarVO visitorCarVOS = visitorPlateService.selectVisitorInfo(vehicleNo);
        if (visitorCarVOS == null) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "无当前访客信息");
        }
        visitorCarVOS.setCount(20);
        return CommonResult.success(visitorCarVOS);
    }


    /**
     * 查询车牌账户信息
     *
     * @param vehicleNo
     * @return
     */
    @PostMapping("/plate")
    @Operation(
            summary = "查询车牌账户信息",
            description = "根据车牌号查询账户余额信息",
            parameters = {
                    @Parameter(
                            name = "vehicleNo",
                            in = ParameterIn.PATH,
                            description = "标准车牌号码",
                            schema = @Schema(
                                    type = "string",
                                    pattern = "^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5,6}$",
                                    example = "京A12345"
                            )
                    ),
                    @Parameter(
                            name = "secretKey",
                            in = ParameterIn.QUERY,
                            description = "秘钥",
                            required = true,
                            schema = @Schema(
                                    type = "string",
                                    defaultValue = "true",
                                    example = "true"
                            )
                    ),
                    @Parameter(
                            name = "byCode",
                            in = ParameterIn.QUERY,
                            description = "工号",
                            schema = @Schema(
                                    type = "string",
                                    defaultValue = "123",
                                    example = "123"
                            )
                    )
            }
    )
    public CommonResult<AccountInfo> getByPlateNumberMoney(
            @RequestParam(value = "vehicleNo", required = false) String vehicleNo,

            @RequestParam(value = "secretKey", required = true) String secretKey,
            @RequestParam(value = "byCode", required = false) String byCode) { // 改为 @RequestParam

        if (secretKey == null || secretKey.isEmpty()) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "请求参数不正常");
        }
        if (!"eyJhbGciOiJSUzI1NiIsImtpZCI6IjNlNzk2NDZjN".equals(secretKey)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "秘钥错误");
        }
        if ((vehicleNo == null || vehicleNo.isEmpty()) && (byCode == null || byCode.isEmpty())) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "车牌或者工号至少有一个");
        }
        AccountInfo account = new AccountInfo();
        //先判断是否为内部车辆
        CardTeachStudInfo cardTeachStudInfo = visitorPlateService.queryByPlateNoAndInfoIdInt(vehicleNo, byCode);
        boolean storedValueCard = true;

        if (cardTeachStudInfo == null) {
            //走访客车辆逻辑
            //查找被访人信息进行扣款
            //先默认
            VisitorCarVO visitorCarVO = visitorPlateService.selectVisitorInfo(vehicleNo);
            if (visitorCarVO == null) {
                return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "当前车牌未注册");
            }
            storedValueCard = false;
            account.setPlateNumber(vehicleNo);
        } else {
            account.setIdCard(cardTeachStudInfo.getIdCard() == null ? "" : cardTeachStudInfo.getIdCard());
            account.setMobile(cardTeachStudInfo.getMobile() == null ? "" : cardTeachStudInfo.getMobile());
            account.setSex(cardTeachStudInfo.getSex() == null ? "" : cardTeachStudInfo.getSex());
            account.setDepartment(cardTeachStudInfo.getDepartment() == null ? "" : cardTeachStudInfo.getDepartment());
            account.setPlateNumber(cardTeachStudInfo.getPlateNo() == null ? "" : cardTeachStudInfo.getPlateNo());
            account.setByCode(cardTeachStudInfo.getByCode() == null ? "" : cardTeachStudInfo.getByCode());
            account.setByName(cardTeachStudInfo.getByName() == null ? "" : cardTeachStudInfo.getByName());
        }
        CardInfo accountInfoById = visitorPlateService.getAccountInfoAndCodeById(vehicleNo, byCode);
        if (accountInfoById == null) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "系统不存在该车牌");
        }
        BigDecimal balance = accountInfoById.getBalance();
        if (balance == null) {
            balance = BigDecimal.ZERO;
        }
        BigDecimal money = visitorPlateService.getConsumeByPlateNo(accountInfoById.getCardId());
        if (money == null) {
            money = BigDecimal.ZERO;
        }
        balance = balance.subtract(money).setScale(2, RoundingMode.HALF_UP);  // 确保两位小数;

        long amountInFen = balance.multiply(new BigDecimal("100")).longValue();
        account.setBalance(amountInFen);
        account.setStoredValueCard(storedValueCard);
        return CommonResult.success(account);
    }

}