<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunmai.ymparking.mapper.VisitorPlateMapper">

    <!-- 插入一条停车信息 -->
    <insert id="savePlateConsumeRecord" parameterType="com.yunmai.ymparking.domain.ConsumePayRecord">
        INSERT INTO `tb_consume_payrecords` (`uid`, `cardid`, `cardno`, `recordtime`, `money`, `machineid`,
                                             `eventcode`, `paytype`, `devid`, `merid`, `placeid`, `schemeid`,
                                             `infoid`, `createdate`, `status`, `serviceuid`, `paywallet`, `des`)
        VALUES (#{uid}, #{cardId}, #{cardNo},
                now(), #{money}, #{machineId}, 0, '1', #{devId},
                #{merId}, #{placeId},
                #{schemeId}, #{infoId},
                now(), 1, '1', 1, #{remark});
    </insert>



    <select id="queryByPlateNoAndInfoIdInt" resultType="com.yunmai.ymparking.domain.CardTeachStudInfo">
        SELECT
        name, idcard AS idCard, mobile,code byCode,plateno as plateNo,name byName,
        CASE WHEN sex = 1 THEN '男' WHEN sex = 2 THEN '女' ELSE '未知' END AS sex,
        getminiorgname(orgcode) AS department
        FROM tb_card_teachstudinfo
        <where>
            status = 1
            AND (
            <!-- 满足 plateNo 或 byCode 任意一个条件即可 -->
            <if test="plateNo != null and plateNo != ''">
                plateno = #{plateNo}
            </if>
            <if test="byCode != null and byCode != ''">
                <!-- 如果 plateNo 也有值，添加 OR -->
                <if test="plateNo != null and plateNo != ''"> OR </if>
                code = #{byCode}
            </if>
            <!-- 如果两者都为空，返回空结果 -->
            <if test="(plateNo == null or plateNo == '') and (byCode == null or byCode == '')">
                1 = 0
            </if>
            )
        </where>
    </select>

    <select id="queryByPlateNo" resultType="com.yunmai.ymparking.domain.CardTeachStudInfo">
        select name,idcard idCard,mobile,code byCode,name byName,
               CASE WHEN sex = 1 THEN '男' WHEN sex = 2 THEN '女' ELSE '未知' END AS sex ,
               getminiorgname(orgcode) department
        from tb_card_teachstudinfo
        <where>
            plateno = #{plateNo}  and status = 1
        </where>
    </select>


    <!-- 查询访客信息 -->
    <select id="selectVisitorInfo" resultType="com.yunmai.ymparking.domain.vo.VisitorCarVO">
        SELECT
            vv.name AS name,
            vv.mobile AS mobile,
            vs.plateno AS plateNumber,
            vs.byvisitor AS byName,
            ct.code as byCode,
            DATE_FORMAT(vs.visittime, '%Y-%m-%d %H:%i:%s') AS validStartTime,
            DATE_FORMAT(vs.leavetime, '%Y-%m-%d %H:%i:%s') AS validEndTime
        FROM
            tb_visitor_subscribe vs
                JOIN
            tb_card_teachstudinfo ct on ct.uid = vs.byvisitorid
                JOIN
            tb_visitor_visitorinfo vv ON vv.uid = vs.visitorid
        WHERE
           now() BETWEEN vs.visittime AND vs.leavetime
          AND vv.status = 1
          AND vs.plateno = #{plateNo}
    </select>


    <select id="getAccountInfoById" resultType="com.yunmai.ymparking.domain.CardInfo">
        SELECT balance,plateno as plateNo,cc.uid as cardId,ct.uid infoId,ct.card cardNo
        from tb_card_teachstudinfo ct join tb_card_cardinfo cc on cc.infoid = ct.uid
        where ct.plateno =  #{plateNo}
          and ct.status = 1
          and cc.status = 1
        order by cc.createdate desc
        limit 1
    </select>

    <select id="getConsumeByPlateNo" resultType="java.math.BigDecimal">
        SELECT sum(t.money) as result FROM (
                                               SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=1 and status=1 and orderstatus=2 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=2 and status=1 and orderstatus=2 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=3 and status=1 and orderstatus=2 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=6 and status=1 and orderstatus=2 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=7 and status=1 and orderstatus=2 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=8 and status=1 and orderstatus=2 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='1' and status=1 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='3' and status=1 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='4' and status=1 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='5' and status=1 and paywallet=1 and cardid=#{cardId}
                                               UNION ALL
                                               SELECT sum(crl.money)*-1 as money FROM tb_consume_reserve_list crl
                                                                                          LEFT JOIN tb_card_cardinfo cc ON crl.infoid=cc.infoid
                                               WHERE crl.isspentfree=1 and crl.isconsumed=0 and crl.status=1 and crl.paywallet=1 and cc.uid=#{cardId}
                                           ) t
    </select>

    <select id="getAccountInfoAndCodeById" resultType="com.yunmai.ymparking.domain.CardInfo">
        SELECT balance,plateno as plateNo,cc.uid as cardId,ct.uid infoId,ct.card cardNo
        from tb_card_teachstudinfo ct join tb_card_cardinfo cc on cc.infoid = ct.uid
        where
           ct.status = 1
          and cc.status = 1
        AND (
        <!-- 满足 plateNo 或 byCode 任意一个条件即可 -->
        <if test="plateNo != null and plateNo != ''">
            plateno = #{plateNo}
        </if>
        <if test="byCode != null and byCode != ''">
            <!-- 如果 plateNo 也有值，添加 OR -->
            <if test="plateNo != null and plateNo != ''"> OR </if>
            code = #{byCode}
        </if>
        <!-- 如果两者都为空，返回空结果 -->
        <if test="(plateNo == null or plateNo == '') and (byCode == null or byCode == '')">
            1 = 0
        </if>
        )
        order by cc.createdate desc
            limit 1
    </select>


</mapper>