-- 为tb_consume_payrecords表添加唯一约束，防止重复扣费订单
-- 基于cardid、devid、money、recordtime（精确到分钟）创建唯一索引

-- 方案1：严格防重复（相同卡、设备、金额、同一分钟内只能有一条记录）
ALTER TABLE tb_consume_payrecords 
ADD UNIQUE INDEX uk_consume_no_duplicate (
    cardid, 
    devid, 
    money, 
    DATE_FORMAT(recordtime, '%Y-%m-%d %H:%i')
);

-- 方案2：如果上面太严格，可以用这个（相同卡、设备、金额、同一秒内只能有一条记录）
-- ALTER TABLE tb_consume_payrecords 
-- ADD UNIQUE INDEX uk_consume_no_duplicate (
--     cardid, 
--     devid, 
--     money, 
--     recordtime
-- );
